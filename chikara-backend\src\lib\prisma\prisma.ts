import { PrismaClient } from "@prisma/client";
import { PrismaMariaDb } from "@prisma/adapter-mariadb";

import questExtensions from "./questExtensions.js";
import shopExtensions from "./shopExtensions.js";
import userExtensions from "./userExtensions.js";
import itemExtensions from "./itemExtensions.js";

const adapter = new PrismaMariaDb({
    connectionUri: process.env.DATABASE_URL || "mysql://root:secret@192.168.0.34:3306/chikara",
    connectionLimit: 5,
});

// Create and export the extended client
const prismaClient = new PrismaClient({ adapter })
    .$extends(userExtensions)
    .$extends(shopExtensions)
    .$extends(questExtensions)
    .$extends(itemExtensions);

export default prismaClient;
