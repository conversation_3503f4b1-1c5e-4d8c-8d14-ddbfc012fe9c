import { PrismaClient } from "@prisma/client";
import { PrismaMariaDb } from "@prisma/adapter-mariadb";

import questExtensions from "./questExtensions.js";
import shopExtensions from "./shopExtensions.js";
import userExtensions from "./userExtensions.js";
import itemExtensions from "./itemExtensions.js";

const adapter = new PrismaMariaDb({
    host: "************",
    port: 3306,
    database: "chikara",
    user: "root",
    password: "secret",
    connectionLimit: 5,
});

// Create and export the extended client
const prismaClient = new PrismaClient({ adapter })
    .$extends(userExtensions)
    .$extends(shopExtensions)
    .$extends(questExtensions)
    .$extends(itemExtensions);

export default prismaClient;
