import { PrismaClient } from "@prisma/client";
import { PrismaMariaDb } from "@prisma/adapter-mariadb";

import questExtensions from "./questExtensions.js";
import shopExtensions from "./shopExtensions.js";
import userExtensions from "./userExtensions.js";
import itemExtensions from "./itemExtensions.js";

// Parse DATABASE_URL or use fallback values
function parseDatabaseUrl(url: string) {
    try {
        const parsed = new URL(url);
        return {
            host: parsed.hostname,
            port: parseInt(parsed.port) || 3306,
            database: parsed.pathname.slice(1), // Remove leading slash
            user: parsed.username,
            password: parsed.password,
        };
    } catch {
        // Fallback to original hardcoded values
        return {
            host: "************",
            port: 3306,
            database: "chikara",
            user: "root",
            password: "secret",
        };
    }
}

const databaseUrl = process.env.DATABASE_URL || "mysql://root:secret@************:3306/chikara";
const dbConfig = parseDatabaseUrl(databaseUrl);

const adapter = new PrismaMariaDb({
    host: dbConfig.host,
    port: dbConfig.port,
    database: dbConfig.database,
    user: dbConfig.user,
    password: dbConfig.password,
    connectionLimit: 5,
});

// Create and export the extended client
const prismaClient = new PrismaClient({ adapter })
    .$extends(userExtensions)
    .$extends(shopExtensions)
    .$extends(questExtensions)
    .$extends(itemExtensions);

export default prismaClient;
