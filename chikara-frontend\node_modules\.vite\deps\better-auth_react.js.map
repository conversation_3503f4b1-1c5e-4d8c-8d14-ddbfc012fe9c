{"version": 3, "sources": ["../../../../node_modules/.bun/better-auth@1.3.2+dfc90389513bb65c/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs", "../../../../node_modules/.bun/better-auth@1.3.2+dfc90389513bb65c/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs", "../../../../node_modules/.bun/better-auth@1.3.2+dfc90389513bb65c/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs", "../../../../node_modules/.bun/better-auth@1.3.2+dfc90389513bb65c/node_modules/better-auth/dist/client/react/index.mjs"], "sourcesContent": ["import { e as env } from './better-auth.8zoxzg-F.mjs';\nimport { B as BetterAuthError } from './better-auth.DdzSJf-n.mjs';\n\nfunction checkHasPath(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.pathname !== \"/\";\n  } catch (error) {\n    throw new BetterAuthError(\n      `Invalid base URL: ${url}. Please provide a valid base URL.`\n    );\n  }\n}\nfunction withPath(url, path = \"/api/auth\") {\n  const hasPath = checkHasPath(url);\n  if (hasPath) {\n    return url;\n  }\n  path = path.startsWith(\"/\") ? path : `/${path}`;\n  return `${url.replace(/\\/+$/, \"\")}${path}`;\n}\nfunction getBaseURL(url, path, request) {\n  if (url) {\n    return withPath(url, path);\n  }\n  const fromEnv = env.BETTER_AUTH_URL || env.NEXT_PUBLIC_BETTER_AUTH_URL || env.PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_AUTH_URL || (env.BASE_URL !== \"/\" ? env.BASE_URL : void 0);\n  if (fromEnv) {\n    return withPath(fromEnv, path);\n  }\n  const fromRequest = request?.headers.get(\"x-forwarded-host\");\n  const fromRequestProto = request?.headers.get(\"x-forwarded-proto\");\n  if (fromRequest && fromRequestProto) {\n    return withPath(`${fromRequestProto}://${fromRequest}`, path);\n  }\n  if (request) {\n    const url2 = getOrigin(request.url);\n    if (!url2) {\n      throw new BetterAuthError(\n        \"Could not get origin from request. Please provide a valid base URL.\"\n      );\n    }\n    return withPath(url2, path);\n  }\n  if (typeof window !== \"undefined\" && window.location) {\n    return withPath(window.location.origin, path);\n  }\n  return void 0;\n}\nfunction getOrigin(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.origin;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getProtocol(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.protocol;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getHost(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.host;\n  } catch (error) {\n    return url;\n  }\n}\n\nexport { getBaseURL as a, getHost as b, getProtocol as c, getOrigin as g };\n", "const PROTO_POLLUTION_PATTERNS = {\n  proto: /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,\n  constructor: /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,\n  protoShort: /\"__proto__\"\\s*:/,\n  constructorShort: /\"constructor\"\\s*:/\n};\nconst JSON_SIGNATURE = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nconst SPECIAL_VALUES = {\n  true: true,\n  false: false,\n  null: null,\n  undefined: void 0,\n  nan: Number.NaN,\n  infinity: Number.POSITIVE_INFINITY,\n  \"-infinity\": Number.NEGATIVE_INFINITY\n};\nconst ISO_DATE_REGEX = /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{1,7}))?(?:Z|([+-])(\\d{2}):(\\d{2}))$/;\nfunction isValidDate(date) {\n  return date instanceof Date && !isNaN(date.getTime());\n}\nfunction parseISODate(value) {\n  const match = ISO_DATE_REGEX.exec(value);\n  if (!match) return null;\n  const [\n    ,\n    year,\n    month,\n    day,\n    hour,\n    minute,\n    second,\n    ms,\n    offsetSign,\n    offsetHour,\n    offsetMinute\n  ] = match;\n  let date = new Date(\n    Date.UTC(\n      parseInt(year, 10),\n      parseInt(month, 10) - 1,\n      parseInt(day, 10),\n      parseInt(hour, 10),\n      parseInt(minute, 10),\n      parseInt(second, 10),\n      ms ? parseInt(ms.padEnd(3, \"0\"), 10) : 0\n    )\n  );\n  if (offsetSign) {\n    const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === \"+\" ? -1 : 1);\n    date.setUTCMinutes(date.getUTCMinutes() + offset);\n  }\n  return isValidDate(date) ? date : null;\n}\nfunction betterJSONParse(value, options = {}) {\n  const {\n    strict = false,\n    warnings = false,\n    reviver,\n    parseDates = true\n  } = options;\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  const trimmed = value.trim();\n  if (trimmed[0] === '\"' && trimmed.endsWith('\"') && !trimmed.slice(1, -1).includes('\"')) {\n    return trimmed.slice(1, -1);\n  }\n  const lowerValue = trimmed.toLowerCase();\n  if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {\n    return SPECIAL_VALUES[lowerValue];\n  }\n  if (!JSON_SIGNATURE.test(trimmed)) {\n    if (strict) {\n      throw new SyntaxError(\"[better-json] Invalid JSON\");\n    }\n    return value;\n  }\n  const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(\n    ([key, pattern]) => {\n      const matches = pattern.test(trimmed);\n      if (matches && warnings) {\n        console.warn(\n          `[better-json] Detected potential prototype pollution attempt using ${key} pattern`\n        );\n      }\n      return matches;\n    }\n  );\n  if (hasProtoPattern && strict) {\n    throw new Error(\n      \"[better-json] Potential prototype pollution attempt detected\"\n    );\n  }\n  try {\n    const secureReviver = (key, value2) => {\n      if (key === \"__proto__\" || key === \"constructor\" && value2 && typeof value2 === \"object\" && \"prototype\" in value2) {\n        if (warnings) {\n          console.warn(\n            `[better-json] Dropping \"${key}\" key to prevent prototype pollution`\n          );\n        }\n        return void 0;\n      }\n      if (parseDates && typeof value2 === \"string\") {\n        const date = parseISODate(value2);\n        if (date) {\n          return date;\n        }\n      }\n      return reviver ? reviver(key, value2) : value2;\n    };\n    return JSON.parse(trimmed, secureReviver);\n  } catch (error) {\n    if (strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction parseJSON(value, options = { strict: true }) {\n  return betterJSONParse(value, options);\n}\n\nexport { parseJSON as p };\n", "import { createFetch } from '@better-fetch/fetch';\nimport { a as getBaseURL } from './better-auth.VTXNLFMT.mjs';\nimport { atom } from 'nanostores';\nimport { u as useAuthQuery } from './better-auth.Buni1mmI.mjs';\nimport { p as parseJSON } from './better-auth.ffWeg50w.mjs';\n\nconst redirectPlugin = {\n  id: \"redirect\",\n  name: \"Redirect\",\n  hooks: {\n    onSuccess(context) {\n      if (context.data?.url && context.data?.redirect) {\n        if (typeof window !== \"undefined\" && window.location) {\n          if (window.location) {\n            try {\n              window.location.href = context.data.url;\n            } catch {\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nfunction getSessionAtom($fetch) {\n  const $signal = atom(false);\n  const session = useAuthQuery($signal, \"/get-session\", $fetch, {\n    method: \"GET\"\n  });\n  return {\n    session,\n    $sessionSignal: $signal\n  };\n}\n\nconst getClientConfig = (options) => {\n  const isCredentialsSupported = \"credentials\" in Request.prototype;\n  const baseURL = getBaseURL(options?.baseURL, options?.basePath);\n  const pluginsFetchPlugins = options?.plugins?.flatMap((plugin) => plugin.fetchPlugins).filter((pl) => pl !== void 0) || [];\n  const lifeCyclePlugin = {\n    id: \"lifecycle-hooks\",\n    name: \"lifecycle-hooks\",\n    hooks: {\n      onSuccess: options?.fetchOptions?.onSuccess,\n      onError: options?.fetchOptions?.onError,\n      onRequest: options?.fetchOptions?.onRequest,\n      onResponse: options?.fetchOptions?.onResponse\n    }\n  };\n  const { onSuccess, onError, onRequest, onResponse, ...restOfFetchOptions } = options?.fetchOptions || {};\n  const $fetch = createFetch({\n    baseURL,\n    ...isCredentialsSupported ? { credentials: \"include\" } : {},\n    method: \"GET\",\n    jsonParser(text) {\n      if (!text) {\n        return null;\n      }\n      return parseJSON(text, {\n        strict: false\n      });\n    },\n    customFetchImpl: async (input, init) => {\n      try {\n        return await fetch(input, init);\n      } catch (error) {\n        return Response.error();\n      }\n    },\n    ...restOfFetchOptions,\n    plugins: [\n      lifeCyclePlugin,\n      ...restOfFetchOptions.plugins || [],\n      ...options?.disableDefaultFetchPlugins ? [] : [redirectPlugin],\n      ...pluginsFetchPlugins\n    ]\n  });\n  const { $sessionSignal, session } = getSessionAtom($fetch);\n  const plugins = options?.plugins || [];\n  let pluginsActions = {};\n  let pluginsAtoms = {\n    $sessionSignal,\n    session\n  };\n  let pluginPathMethods = {\n    \"/sign-out\": \"POST\",\n    \"/revoke-sessions\": \"POST\",\n    \"/revoke-other-sessions\": \"POST\",\n    \"/delete-user\": \"POST\"\n  };\n  const atomListeners = [\n    {\n      signal: \"$sessionSignal\",\n      matcher(path) {\n        return path === \"/sign-out\" || path === \"/update-user\" || path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path === \"/delete-user\" || path === \"/verify-email\";\n      }\n    }\n  ];\n  for (const plugin of plugins) {\n    if (plugin.getAtoms) {\n      Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));\n    }\n    if (plugin.pathMethods) {\n      Object.assign(pluginPathMethods, plugin.pathMethods);\n    }\n    if (plugin.atomListeners) {\n      atomListeners.push(...plugin.atomListeners);\n    }\n  }\n  const $store = {\n    notify: (signal) => {\n      pluginsAtoms[signal].set(\n        !pluginsAtoms[signal].get()\n      );\n    },\n    listen: (signal, listener) => {\n      pluginsAtoms[signal].subscribe(listener);\n    },\n    atoms: pluginsAtoms\n  };\n  for (const plugin of plugins) {\n    if (plugin.getActions) {\n      Object.assign(\n        pluginsActions,\n        plugin.getActions?.($fetch, $store, options)\n      );\n    }\n  }\n  return {\n    pluginsActions,\n    pluginsAtoms,\n    pluginPathMethods,\n    atomListeners,\n    $fetch,\n    $store\n  };\n};\n\nfunction getMethod(path, knownPathMethods, args) {\n  const method = knownPathMethods[path];\n  const { fetchOptions, query, ...body } = args || {};\n  if (method) {\n    return method;\n  }\n  if (fetchOptions?.method) {\n    return fetchOptions.method;\n  }\n  if (body && Object.keys(body).length > 0) {\n    return \"POST\";\n  }\n  return \"GET\";\n}\nfunction createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {\n  function createProxy(path = []) {\n    return new Proxy(function() {\n    }, {\n      get(target, prop) {\n        const fullPath = [...path, prop];\n        let current = routes;\n        for (const segment of fullPath) {\n          if (current && typeof current === \"object\" && segment in current) {\n            current = current[segment];\n          } else {\n            current = void 0;\n            break;\n          }\n        }\n        if (typeof current === \"function\") {\n          return current;\n        }\n        return createProxy(fullPath);\n      },\n      apply: async (_, __, args) => {\n        const routePath = \"/\" + path.map(\n          (segment) => segment.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`)\n        ).join(\"/\");\n        const arg = args[0] || {};\n        const fetchOptions = args[1] || {};\n        const { query, fetchOptions: argFetchOptions, ...body } = arg;\n        const options = {\n          ...fetchOptions,\n          ...argFetchOptions\n        };\n        const method = getMethod(routePath, knownPathMethods, arg);\n        return await client(routePath, {\n          ...options,\n          body: method === \"GET\" ? void 0 : {\n            ...body,\n            ...options?.body || {}\n          },\n          query: query || options?.query,\n          method,\n          async onSuccess(context) {\n            await options?.onSuccess?.(context);\n            const matches = atomListeners?.find((s) => s.matcher(routePath));\n            if (!matches) return;\n            const signal = atoms[matches.signal];\n            if (!signal) return;\n            const val = signal.get();\n            setTimeout(() => {\n              signal.set(!val);\n            }, 10);\n          }\n        });\n      }\n    });\n  }\n  return createProxy();\n}\n\nexport { createDynamicPathProxy as c, getClientConfig as g };\n", "import { g as getClientConfig, c as createDynamicPathProxy } from '../../shared/better-auth.A_Crzln-.mjs';\nimport { listenKeys } from 'nanostores';\nimport { useRef, useCallback, useSyncExternalStore } from 'react';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.Buni1mmI.mjs';\nimport '../../shared/better-auth.ffWeg50w.mjs';\n\nfunction useStore(store, options = {}) {\n  let snapshotRef = useRef(store.get());\n  const { keys, deps = [store, keys] } = options;\n  let subscribe = useCallback((onChange) => {\n    const emitChange = (value) => {\n      if (snapshotRef.current === value) return;\n      snapshotRef.current = value;\n      onChange();\n    };\n    emitChange(store.value);\n    if (keys?.length) {\n      return listenKeys(store, keys, emitChange);\n    }\n    return store.listen(emitChange);\n  }, deps);\n  let get = () => snapshotRef.current;\n  return useSyncExternalStore(subscribe, get, get);\n}\n\nfunction getAtomKey(str) {\n  return `use${capitalizeFirstLetter(str)}`;\n}\nfunction capitalizeFirstLetter(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction createAuthClient(options) {\n  const {\n    pluginPathMethods,\n    pluginsActions,\n    pluginsAtoms,\n    $fetch,\n    $store,\n    atomListeners\n  } = getClientConfig(options);\n  let resolvedHooks = {};\n  for (const [key, value] of Object.entries(pluginsAtoms)) {\n    resolvedHooks[getAtomKey(key)] = () => useStore(value);\n  }\n  const routes = {\n    ...pluginsActions,\n    ...resolvedHooks,\n    $fetch,\n    $store\n  };\n  const proxy = createDynamicPathProxy(\n    routes,\n    $fetch,\n    pluginPathMethods,\n    pluginsAtoms,\n    atomListeners\n  );\n  return proxy;\n}\n\nexport { capitalizeFirstLetter, createAuthClient, useStore };\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,SAAS,aAAa,KAAK;AACzB,MAAI;AACF,UAAM,YAAY,IAAI,IAAI,GAAG;AAC7B,WAAO,UAAU,aAAa;AAAA,EAChC,SAAS,OAAO;AACd,UAAM,IAAI;AAAA,MACR,qBAAqB,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,SAAS,KAAK,OAAO,aAAa;AACzC,QAAM,UAAU,aAAa,GAAG;AAChC,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,SAAO,KAAK,WAAW,GAAG,IAAI,OAAO,IAAI,IAAI;AAC7C,SAAO,GAAG,IAAI,QAAQ,QAAQ,EAAE,CAAC,GAAG,IAAI;AAC1C;AACA,SAAS,WAAW,KAAK,MAAM,SAAS;AACtC,MAAI,KAAK;AACP,WAAO,SAAS,KAAK,IAAI;AAAA,EAC3B;AACA,QAAM,UAAU,IAAI,mBAAmB,IAAI,+BAA+B,IAAI,0BAA0B,IAAI,+BAA+B,IAAI,yBAAyB,IAAI,aAAa,MAAM,IAAI,WAAW;AAC9M,MAAI,SAAS;AACX,WAAO,SAAS,SAAS,IAAI;AAAA,EAC/B;AACA,QAAM,cAAc,SAAS,QAAQ,IAAI,kBAAkB;AAC3D,QAAM,mBAAmB,SAAS,QAAQ,IAAI,mBAAmB;AACjE,MAAI,eAAe,kBAAkB;AACnC,WAAO,SAAS,GAAG,gBAAgB,MAAM,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,MAAI,SAAS;AACX,UAAM,OAAO,UAAU,QAAQ,GAAG;AAClC,QAAI,CAAC,MAAM;AACT,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,MAAM,IAAI;AAAA,EAC5B;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,WAAO,SAAS,OAAO,SAAS,QAAQ,IAAI;AAAA,EAC9C;AACA,SAAO;AACT;AACA,SAAS,UAAU,KAAK;AACtB,MAAI;AACF,UAAM,YAAY,IAAI,IAAI,GAAG;AAC7B,WAAO,UAAU;AAAA,EACnB,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AACF;;;ACvDA,IAAM,2BAA2B;AAAA,EAC/B,OAAO;AAAA,EACP,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,kBAAkB;AACpB;AACA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AAAA,EACX,KAAK,OAAO;AAAA,EACZ,UAAU,OAAO;AAAA,EACjB,aAAa,OAAO;AACtB;AACA,IAAM,iBAAiB;AACvB,SAAS,YAAY,MAAM;AACzB,SAAO,gBAAgB,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC;AACtD;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,QAAQ,eAAe,KAAK,KAAK;AACvC,MAAI,CAAC,MAAO,QAAO;AACnB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,IAAI;AAAA,IACb,KAAK;AAAA,MACH,SAAS,MAAM,EAAE;AAAA,MACjB,SAAS,OAAO,EAAE,IAAI;AAAA,MACtB,SAAS,KAAK,EAAE;AAAA,MAChB,SAAS,MAAM,EAAE;AAAA,MACjB,SAAS,QAAQ,EAAE;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,MACnB,KAAK,SAAS,GAAG,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI;AAAA,IACzC;AAAA,EACF;AACA,MAAI,YAAY;AACd,UAAM,UAAU,SAAS,YAAY,EAAE,IAAI,KAAK,SAAS,cAAc,EAAE,MAAM,eAAe,MAAM,KAAK;AACzG,SAAK,cAAc,KAAK,cAAc,IAAI,MAAM;AAAA,EAClD;AACA,SAAO,YAAY,IAAI,IAAI,OAAO;AACpC;AACA,SAAS,gBAAgB,OAAO,UAAU,CAAC,GAAG;AAC5C,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,WAAW;AAAA,IACX;AAAA,IACA,aAAa;AAAA,EACf,IAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM,KAAK;AAC3B,MAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,MAAM,GAAG,EAAE,EAAE,SAAS,GAAG,GAAG;AACtF,WAAO,QAAQ,MAAM,GAAG,EAAE;AAAA,EAC5B;AACA,QAAM,aAAa,QAAQ,YAAY;AACvC,MAAI,WAAW,UAAU,KAAK,cAAc,gBAAgB;AAC1D,WAAO,eAAe,UAAU;AAAA,EAClC;AACA,MAAI,CAAC,eAAe,KAAK,OAAO,GAAG;AACjC,QAAI,QAAQ;AACV,YAAM,IAAI,YAAY,4BAA4B;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,OAAO,QAAQ,wBAAwB,EAAE;AAAA,IAC/D,CAAC,CAAC,KAAK,OAAO,MAAM;AAClB,YAAM,UAAU,QAAQ,KAAK,OAAO;AACpC,UAAI,WAAW,UAAU;AACvB,gBAAQ;AAAA,UACN,sEAAsE,GAAG;AAAA,QAC3E;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACF,UAAM,gBAAgB,CAAC,KAAK,WAAW;AACrC,UAAI,QAAQ,eAAe,QAAQ,iBAAiB,UAAU,OAAO,WAAW,YAAY,eAAe,QAAQ;AACjH,YAAI,UAAU;AACZ,kBAAQ;AAAA,YACN,2BAA2B,GAAG;AAAA,UAChC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,UAAI,cAAc,OAAO,WAAW,UAAU;AAC5C,cAAM,OAAO,aAAa,MAAM;AAChC,YAAI,MAAM;AACR,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,UAAU,QAAQ,KAAK,MAAM,IAAI;AAAA,IAC1C;AACA,WAAO,KAAK,MAAM,SAAS,aAAa;AAAA,EAC1C,SAAS,OAAO;AACd,QAAI,QAAQ;AACV,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,OAAO,UAAU,EAAE,QAAQ,KAAK,GAAG;AACpD,SAAO,gBAAgB,OAAO,OAAO;AACvC;;;ACnHA,IAAM,iBAAiB;AAAA,EACrB,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,OAAO;AAAA,IACL,UAAU,SAAS;AACjB,UAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,UAAU;AAC/C,YAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,cAAI,OAAO,UAAU;AACnB,gBAAI;AACF,qBAAO,SAAS,OAAO,QAAQ,KAAK;AAAA,YACtC,QAAQ;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,eAAe,QAAQ;AAC9B,QAAM,UAAU,KAAK,KAAK;AAC1B,QAAM,UAAU,aAAa,SAAS,gBAAgB,QAAQ;AAAA,IAC5D,QAAQ;AAAA,EACV,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA,gBAAgB;AAAA,EAClB;AACF;AAEA,IAAM,kBAAkB,CAAC,YAAY;AACnC,QAAM,yBAAyB,iBAAiB,QAAQ;AACxD,QAAM,UAAU,WAAW,SAAS,SAAS,SAAS,QAAQ;AAC9D,QAAM,sBAAsB,SAAS,SAAS,QAAQ,CAAC,WAAW,OAAO,YAAY,EAAE,OAAO,CAAC,OAAO,OAAO,MAAM,KAAK,CAAC;AACzH,QAAM,kBAAkB;AAAA,IACtB,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,MACL,WAAW,SAAS,cAAc;AAAA,MAClC,SAAS,SAAS,cAAc;AAAA,MAChC,WAAW,SAAS,cAAc;AAAA,MAClC,YAAY,SAAS,cAAc;AAAA,IACrC;AAAA,EACF;AACA,QAAM,EAAE,WAAW,SAAS,WAAW,YAAY,GAAG,mBAAmB,IAAI,SAAS,gBAAgB,CAAC;AACvG,QAAM,SAAS,YAAY;AAAA,IACzB;AAAA,IACA,GAAG,yBAAyB,EAAE,aAAa,UAAU,IAAI,CAAC;AAAA,IAC1D,QAAQ;AAAA,IACR,WAAW,MAAM;AACf,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AACA,aAAO,UAAU,MAAM;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,iBAAiB,OAAO,OAAO,SAAS;AACtC,UAAI;AACF,eAAO,MAAM,MAAM,OAAO,IAAI;AAAA,MAChC,SAAS,OAAO;AACd,eAAO,SAAS,MAAM;AAAA,MACxB;AAAA,IACF;AAAA,IACA,GAAG;AAAA,IACH,SAAS;AAAA,MACP;AAAA,MACA,GAAG,mBAAmB,WAAW,CAAC;AAAA,MAClC,GAAG,SAAS,6BAA6B,CAAC,IAAI,CAAC,cAAc;AAAA,MAC7D,GAAG;AAAA,IACL;AAAA,EACF,CAAC;AACD,QAAM,EAAE,gBAAgB,QAAQ,IAAI,eAAe,MAAM;AACzD,QAAM,UAAU,SAAS,WAAW,CAAC;AACrC,MAAI,iBAAiB,CAAC;AACtB,MAAI,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACA,MAAI,oBAAoB;AAAA,IACtB,aAAa;AAAA,IACb,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,gBAAgB;AAAA,EAClB;AACA,QAAM,gBAAgB;AAAA,IACpB;AAAA,MACE,QAAQ;AAAA,MACR,QAAQ,MAAM;AACZ,eAAO,SAAS,eAAe,SAAS,kBAAkB,KAAK,WAAW,UAAU,KAAK,KAAK,WAAW,UAAU,KAAK,SAAS,kBAAkB,SAAS;AAAA,MAC9J;AAAA,IACF;AAAA,EACF;AACA,aAAW,UAAU,SAAS;AAC5B,QAAI,OAAO,UAAU;AACnB,aAAO,OAAO,cAAc,OAAO,WAAW,MAAM,CAAC;AAAA,IACvD;AACA,QAAI,OAAO,aAAa;AACtB,aAAO,OAAO,mBAAmB,OAAO,WAAW;AAAA,IACrD;AACA,QAAI,OAAO,eAAe;AACxB,oBAAc,KAAK,GAAG,OAAO,aAAa;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,QAAQ,CAAC,WAAW;AAClB,mBAAa,MAAM,EAAE;AAAA,QACnB,CAAC,aAAa,MAAM,EAAE,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,QAAQ,aAAa;AAC5B,mBAAa,MAAM,EAAE,UAAU,QAAQ;AAAA,IACzC;AAAA,IACA,OAAO;AAAA,EACT;AACA,aAAW,UAAU,SAAS;AAC5B,QAAI,OAAO,YAAY;AACrB,aAAO;AAAA,QACL;AAAA,QACA,OAAO,aAAa,QAAQ,QAAQ,OAAO;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,MAAM,kBAAkB,MAAM;AAC/C,QAAM,SAAS,iBAAiB,IAAI;AACpC,QAAM,EAAE,cAAc,OAAO,GAAG,KAAK,IAAI,QAAQ,CAAC;AAClD,MAAI,QAAQ;AACV,WAAO;AAAA,EACT;AACA,MAAI,cAAc,QAAQ;AACxB,WAAO,aAAa;AAAA,EACtB;AACA,MAAI,QAAQ,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,QAAQ,QAAQ,kBAAkB,OAAO,eAAe;AACtF,WAAS,YAAY,OAAO,CAAC,GAAG;AAC9B,WAAO,IAAI,MAAM,WAAW;AAAA,IAC5B,GAAG;AAAA,MACD,IAAI,QAAQ,MAAM;AAChB,cAAM,WAAW,CAAC,GAAG,MAAM,IAAI;AAC/B,YAAI,UAAU;AACd,mBAAW,WAAW,UAAU;AAC9B,cAAI,WAAW,OAAO,YAAY,YAAY,WAAW,SAAS;AAChE,sBAAU,QAAQ,OAAO;AAAA,UAC3B,OAAO;AACL,sBAAU;AACV;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO,YAAY,YAAY;AACjC,iBAAO;AAAA,QACT;AACA,eAAO,YAAY,QAAQ;AAAA,MAC7B;AAAA,MACA,OAAO,OAAO,GAAG,IAAI,SAAS;AAC5B,cAAM,YAAY,MAAM,KAAK;AAAA,UAC3B,CAAC,YAAY,QAAQ,QAAQ,UAAU,CAAC,WAAW,IAAI,OAAO,YAAY,CAAC,EAAE;AAAA,QAC/E,EAAE,KAAK,GAAG;AACV,cAAM,MAAM,KAAK,CAAC,KAAK,CAAC;AACxB,cAAM,eAAe,KAAK,CAAC,KAAK,CAAC;AACjC,cAAM,EAAE,OAAO,cAAc,iBAAiB,GAAG,KAAK,IAAI;AAC1D,cAAM,UAAU;AAAA,UACd,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,cAAM,SAAS,UAAU,WAAW,kBAAkB,GAAG;AACzD,eAAO,MAAM,OAAO,WAAW;AAAA,UAC7B,GAAG;AAAA,UACH,MAAM,WAAW,QAAQ,SAAS;AAAA,YAChC,GAAG;AAAA,YACH,GAAG,SAAS,QAAQ,CAAC;AAAA,UACvB;AAAA,UACA,OAAO,SAAS,SAAS;AAAA,UACzB;AAAA,UACA,MAAM,UAAU,SAAS;AACvB,kBAAM,SAAS,YAAY,OAAO;AAClC,kBAAM,UAAU,eAAe,KAAK,CAAC,MAAM,EAAE,QAAQ,SAAS,CAAC;AAC/D,gBAAI,CAAC,QAAS;AACd,kBAAM,SAAS,MAAM,QAAQ,MAAM;AACnC,gBAAI,CAAC,OAAQ;AACb,kBAAM,MAAM,OAAO,IAAI;AACvB,uBAAW,MAAM;AACf,qBAAO,IAAI,CAAC,GAAG;AAAA,YACjB,GAAG,EAAE;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,YAAY;AACrB;;;AC/MA,mBAA0D;AAQ1D,SAAS,SAAS,OAAO,UAAU,CAAC,GAAG;AACrC,MAAI,kBAAc,qBAAO,MAAM,IAAI,CAAC;AACpC,QAAM,EAAE,MAAM,OAAO,CAAC,OAAO,IAAI,EAAE,IAAI;AACvC,MAAI,gBAAY,0BAAY,CAAC,aAAa;AACxC,UAAM,aAAa,CAAC,UAAU;AAC5B,UAAI,YAAY,YAAY,MAAO;AACnC,kBAAY,UAAU;AACtB,eAAS;AAAA,IACX;AACA,eAAW,MAAM,KAAK;AACtB,QAAI,MAAM,QAAQ;AAChB,aAAO,WAAW,OAAO,MAAM,UAAU;AAAA,IAC3C;AACA,WAAO,MAAM,OAAO,UAAU;AAAA,EAChC,GAAG,IAAI;AACP,MAAI,MAAM,MAAM,YAAY;AAC5B,aAAO,mCAAqB,WAAW,KAAK,GAAG;AACjD;AAEA,SAAS,WAAW,KAAK;AACvB,SAAO,MAAM,sBAAsB,GAAG,CAAC;AACzC;AACA,SAAS,sBAAsB,KAAK;AAClC,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAClD;AACA,SAAS,iBAAiB,SAAS;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,OAAO;AAC3B,MAAI,gBAAgB,CAAC;AACrB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,YAAY,GAAG;AACvD,kBAAc,WAAW,GAAG,CAAC,IAAI,MAAM,SAAS,KAAK;AAAA,EACvD;AACA,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;", "names": []}