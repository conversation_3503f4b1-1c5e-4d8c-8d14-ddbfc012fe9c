{"version": 3, "sources": ["../../../../node_modules/.bun/@orpc+client@1.7.4/node_modules/@orpc/client/dist/index.mjs"], "sourcesContent": ["import { i as isDefinedError } from './shared/client.DHOfWE0c.mjs';\nexport { C as COMMON_ORPC_ERROR_DEFS, O as ORPCError, d as createORPCError<PERSON>rom<PERSON>son, a as fallbackORPCErrorMessage, f as fallbackORPCErrorStatus, c as isORPC<PERSON>rror<PERSON><PERSON>, b as isORPCErrorStatus, m as mapEventIterator, t as toORPCError } from './shared/client.DHOfWE0c.mjs';\nimport { isTypescriptObject } from '@orpc/shared';\nexport { EventPublisher, asyncIteratorToStream as eventIteratorToStream, onError, onFinish, onStart, onSuccess, streamToAsyncIteratorClass as streamToEventIterator } from '@orpc/shared';\nexport { ErrorEvent } from '@orpc/standard-server';\n\nasync function safe(promise) {\n  try {\n    const output = await promise;\n    return Object.assign(\n      [null, output, false, true],\n      { error: null, data: output, isDefined: false, isSuccess: true }\n    );\n  } catch (e) {\n    const error = e;\n    if (isDefinedError(error)) {\n      return Object.assign(\n        [error, void 0, true, false],\n        { error, data: void 0, isDefined: true, isSuccess: false }\n      );\n    }\n    return Object.assign(\n      [error, void 0, false, false],\n      { error, data: void 0, isDefined: false, isSuccess: false }\n    );\n  }\n}\nfunction resolveFriendlyClientOptions(options) {\n  return {\n    ...options,\n    context: options.context ?? {}\n    // Context only optional if all fields are optional\n  };\n}\n\nfunction createORPCClient(link, options = {}) {\n  const path = options.path ?? [];\n  const procedureClient = async (...[input, options2 = {}]) => {\n    return await link.call(path, input, resolveFriendlyClientOptions(options2));\n  };\n  const recursive = new Proxy(procedureClient, {\n    get(target, key) {\n      if (typeof key !== \"string\") {\n        return Reflect.get(target, key);\n      }\n      return createORPCClient(link, {\n        ...options,\n        path: [...path, key]\n      });\n    }\n  });\n  return recursive;\n}\n\nfunction createSafeClient(client) {\n  const proxy = new Proxy((...args) => safe(client(...args)), {\n    get(_, prop, receiver) {\n      const value = Reflect.get(client, prop, receiver);\n      if (typeof prop !== \"string\") {\n        return value;\n      }\n      if (!isTypescriptObject(value)) {\n        return value;\n      }\n      return createSafeClient(value);\n    }\n  });\n  return proxy;\n}\n\nclass DynamicLink {\n  constructor(linkResolver) {\n    this.linkResolver = linkResolver;\n  }\n  async call(path, input, options) {\n    const resolvedLink = await this.linkResolver(options, path, input);\n    const output = await resolvedLink.call(path, input, options);\n    return output;\n  }\n}\n\nexport { DynamicLink, createORPCClient, createSafeClient, isDefinedError, resolveFriendlyClientOptions, safe };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,eAAe,KAAK,SAAS;AAC3B,MAAI;AACF,UAAM,SAAS,MAAM;AACrB,WAAO,OAAO;AAAA,MACZ,CAAC,MAAM,QAAQ,OAAO,IAAI;AAAA,MAC1B,EAAE,OAAO,MAAM,MAAM,QAAQ,WAAW,OAAO,WAAW,KAAK;AAAA,IACjE;AAAA,EACF,SAAS,GAAG;AACV,UAAM,QAAQ;AACd,QAAI,eAAe,KAAK,GAAG;AACzB,aAAO,OAAO;AAAA,QACZ,CAAC,OAAO,QAAQ,MAAM,KAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,QAAQ,WAAW,MAAM,WAAW,MAAM;AAAA,MAC3D;AAAA,IACF;AACA,WAAO,OAAO;AAAA,MACZ,CAAC,OAAO,QAAQ,OAAO,KAAK;AAAA,MAC5B,EAAE,OAAO,MAAM,QAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,IAC5D;AAAA,EACF;AACF;AACA,SAAS,6BAA6B,SAAS;AAC7C,SAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS,QAAQ,WAAW,CAAC;AAAA;AAAA,EAE/B;AACF;AAEA,SAAS,iBAAiB,MAAM,UAAU,CAAC,GAAG;AAC5C,QAAM,OAAO,QAAQ,QAAQ,CAAC;AAC9B,QAAM,kBAAkB,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM;AAC3D,WAAO,MAAM,KAAK,KAAK,MAAM,OAAO,6BAA6B,QAAQ,CAAC;AAAA,EAC5E;AACA,QAAM,YAAY,IAAI,MAAM,iBAAiB;AAAA,IAC3C,IAAI,QAAQ,KAAK;AACf,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,QAAQ,IAAI,QAAQ,GAAG;AAAA,MAChC;AACA,aAAO,iBAAiB,MAAM;AAAA,QAC5B,GAAG;AAAA,QACH,MAAM,CAAC,GAAG,MAAM,GAAG;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ;AAChC,QAAM,QAAQ,IAAI,MAAM,IAAI,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,GAAG;AAAA,IAC1D,IAAI,GAAG,MAAM,UAAU;AACrB,YAAM,QAAQ,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAChD,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,CAAC,mBAAmB,KAAK,GAAG;AAC9B,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,KAAK;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,MAAM,KAAK,MAAM,OAAO,SAAS;AAC/B,UAAM,eAAe,MAAM,KAAK,aAAa,SAAS,MAAM,KAAK;AACjE,UAAM,SAAS,MAAM,aAAa,KAAK,MAAM,OAAO,OAAO;AAC3D,WAAO;AAAA,EACT;AACF;", "names": []}