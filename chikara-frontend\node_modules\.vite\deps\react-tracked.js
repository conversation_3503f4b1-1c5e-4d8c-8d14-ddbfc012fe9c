import {
  require_scheduler
} from "./chunk-KSZDBSGA.js";
import {
  require_react
} from "./chunk-BUSN7M7O.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// ../node_modules/.bun/react-tracked@2.0.1+9c2751b043b8213e/node_modules/react-tracked/dist/createContainer.js
var import_react4 = __toESM(require_react(), 1);

// ../node_modules/.bun/use-context-selector@2.0.0+9c2751b043b8213e/node_modules/use-context-selector/dist/index.js
var import_react = __toESM(require_react());
var import_scheduler = __toESM(require_scheduler());
var CONTEXT_VALUE = Symbol();
var ORIGINAL_PROVIDER = Symbol();
var isSSR = typeof window === "undefined" || /ServerSideRendering/.test(window.navigator && window.navigator.userAgent);
var useIsomorphicLayoutEffect = isSSR ? import_react.useEffect : import_react.useLayoutEffect;
var runWithNormalPriority = import_scheduler.unstable_runWithPriority ? (fn) => {
  try {
    (0, import_scheduler.unstable_runWithPriority)(import_scheduler.unstable_NormalPriority, fn);
  } catch (e) {
    if (e.message === "Not implemented.") {
      fn();
    } else {
      throw e;
    }
  }
} : (fn) => fn();
var createProvider = (ProviderOrig) => {
  const ContextProvider = ({ value, children }) => {
    const valueRef = (0, import_react.useRef)(value);
    const versionRef = (0, import_react.useRef)(0);
    const [resolve, setResolve] = (0, import_react.useState)(null);
    if (resolve) {
      resolve(value);
      setResolve(null);
    }
    const contextValue = (0, import_react.useRef)();
    if (!contextValue.current) {
      const listeners = /* @__PURE__ */ new Set();
      const update = (fn, options) => {
        versionRef.current += 1;
        const action = {
          n: versionRef.current
        };
        if (options === null || options === void 0 ? void 0 : options.suspense) {
          action.n *= -1;
          action.p = new Promise((r) => {
            setResolve(() => (v) => {
              action.v = v;
              delete action.p;
              r(v);
            });
          });
        }
        listeners.forEach((listener) => listener(action));
        fn();
      };
      contextValue.current = {
        [CONTEXT_VALUE]: {
          /* "v"alue     */
          v: valueRef,
          /* versio"n"   */
          n: versionRef,
          /* "l"isteners */
          l: listeners,
          /* "u"pdate    */
          u: update
        }
      };
    }
    useIsomorphicLayoutEffect(() => {
      valueRef.current = value;
      versionRef.current += 1;
      runWithNormalPriority(() => {
        contextValue.current[CONTEXT_VALUE].l.forEach((listener) => {
          listener({ n: versionRef.current, v: value });
        });
      });
    }, [value]);
    return (0, import_react.createElement)(ProviderOrig, { value: contextValue.current }, children);
  };
  return ContextProvider;
};
function createContext(defaultValue) {
  const context = (0, import_react.createContext)({
    [CONTEXT_VALUE]: {
      /* "v"alue     */
      v: { current: defaultValue },
      /* versio"n"   */
      n: { current: -1 },
      /* "l"isteners */
      l: /* @__PURE__ */ new Set(),
      /* "u"pdate    */
      u: (f) => f()
    }
  });
  context[ORIGINAL_PROVIDER] = context.Provider;
  context.Provider = createProvider(context.Provider);
  delete context.Consumer;
  return context;
}
function useContextSelector(context, selector) {
  const contextValue = (0, import_react.useContext)(context)[CONTEXT_VALUE];
  if (typeof process === "object" && true) {
    if (!contextValue) {
      throw new Error("useContextSelector requires special context");
    }
  }
  const {
    /* "v"alue     */
    v: { current: value },
    /* versio"n"   */
    n: { current: version },
    /* "l"isteners */
    l: listeners
  } = contextValue;
  const selected = selector(value);
  const [state, dispatch] = (0, import_react.useReducer)((prev, action) => {
    if (!action) {
      return [value, selected];
    }
    if ("p" in action) {
      throw action.p;
    }
    if (action.n === version) {
      if (Object.is(prev[1], selected)) {
        return prev;
      }
      return [value, selected];
    }
    try {
      if ("v" in action) {
        if (Object.is(prev[0], action.v)) {
          return prev;
        }
        const nextSelected = selector(action.v);
        if (Object.is(prev[1], nextSelected)) {
          return prev;
        }
        return [action.v, nextSelected];
      }
    } catch (_e) {
    }
    return [...prev];
  }, [value, selected]);
  if (!Object.is(state[1], selected)) {
    dispatch();
  }
  useIsomorphicLayoutEffect(() => {
    listeners.add(dispatch);
    return () => {
      listeners.delete(dispatch);
    };
  }, [listeners]);
  return state[1];
}
function useContextUpdate(context) {
  const contextValue = (0, import_react.useContext)(context)[CONTEXT_VALUE];
  if (typeof process === "object" && true) {
    if (!contextValue) {
      throw new Error("useContextUpdate requires special context");
    }
  }
  const { u: update } = contextValue;
  return update;
}

// ../node_modules/.bun/react-tracked@2.0.1+9c2751b043b8213e/node_modules/react-tracked/dist/createTrackedSelector.js
var import_react3 = __toESM(require_react(), 1);

// ../node_modules/.bun/proxy-compare@3.0.1/node_modules/proxy-compare/dist/index.js
var TRACK_MEMO_SYMBOL = Symbol();
var GET_ORIGINAL_SYMBOL = Symbol();
var AFFECTED_PROPERTY = "a";
var IS_TARGET_COPIED_PROPERTY = "f";
var PROXY_PROPERTY = "p";
var PROXY_CACHE_PROPERTY = "c";
var TARGET_CACHE_PROPERTY = "t";
var HAS_KEY_PROPERTY = "h";
var ALL_OWN_KEYS_PROPERTY = "w";
var HAS_OWN_KEY_PROPERTY = "o";
var KEYS_PROPERTY = "k";
var newProxy = (target, handler) => new Proxy(target, handler);
var getProto = Object.getPrototypeOf;
var objectsToTrack = /* @__PURE__ */ new WeakMap();
var isObjectToTrack = (obj) => obj && (objectsToTrack.has(obj) ? objectsToTrack.get(obj) : getProto(obj) === Object.prototype || getProto(obj) === Array.prototype);
var isObject = (x) => typeof x === "object" && x !== null;
var needsToCopyTargetObject = (obj) => Object.values(Object.getOwnPropertyDescriptors(obj)).some((descriptor) => !descriptor.configurable && !descriptor.writable);
var copyTargetObject = (obj) => {
  if (Array.isArray(obj)) {
    return Array.from(obj);
  }
  const descriptors = Object.getOwnPropertyDescriptors(obj);
  Object.values(descriptors).forEach((desc) => {
    desc.configurable = true;
  });
  return Object.create(getProto(obj), descriptors);
};
var createProxyHandler = (origObj, isTargetCopied) => {
  const state = {
    [IS_TARGET_COPIED_PROPERTY]: isTargetCopied
  };
  let trackObject = false;
  const recordUsage = (type, key) => {
    if (!trackObject) {
      let used = state[AFFECTED_PROPERTY].get(origObj);
      if (!used) {
        used = {};
        state[AFFECTED_PROPERTY].set(origObj, used);
      }
      if (type === ALL_OWN_KEYS_PROPERTY) {
        used[ALL_OWN_KEYS_PROPERTY] = true;
      } else {
        let set = used[type];
        if (!set) {
          set = /* @__PURE__ */ new Set();
          used[type] = set;
        }
        set.add(key);
      }
    }
  };
  const recordObjectAsUsed = () => {
    trackObject = true;
    state[AFFECTED_PROPERTY].delete(origObj);
  };
  const handler = {
    get(target, key) {
      if (key === GET_ORIGINAL_SYMBOL) {
        return origObj;
      }
      recordUsage(KEYS_PROPERTY, key);
      return createProxy(Reflect.get(target, key), state[AFFECTED_PROPERTY], state[PROXY_CACHE_PROPERTY], state[TARGET_CACHE_PROPERTY]);
    },
    has(target, key) {
      if (key === TRACK_MEMO_SYMBOL) {
        recordObjectAsUsed();
        return true;
      }
      recordUsage(HAS_KEY_PROPERTY, key);
      return Reflect.has(target, key);
    },
    getOwnPropertyDescriptor(target, key) {
      recordUsage(HAS_OWN_KEY_PROPERTY, key);
      return Reflect.getOwnPropertyDescriptor(target, key);
    },
    ownKeys(target) {
      recordUsage(ALL_OWN_KEYS_PROPERTY);
      return Reflect.ownKeys(target);
    }
  };
  if (isTargetCopied) {
    handler.set = handler.deleteProperty = () => false;
  }
  return [handler, state];
};
var getOriginalObject = (obj) => (
  // unwrap proxy
  obj[GET_ORIGINAL_SYMBOL] || // otherwise
  obj
);
var createProxy = (obj, affected, proxyCache, targetCache) => {
  if (!isObjectToTrack(obj))
    return obj;
  let targetAndCopied = targetCache && targetCache.get(obj);
  if (!targetAndCopied) {
    const target2 = getOriginalObject(obj);
    if (needsToCopyTargetObject(target2)) {
      targetAndCopied = [target2, copyTargetObject(target2)];
    } else {
      targetAndCopied = [target2];
    }
    targetCache === null || targetCache === void 0 ? void 0 : targetCache.set(obj, targetAndCopied);
  }
  const [target, copiedTarget] = targetAndCopied;
  let handlerAndState = proxyCache && proxyCache.get(target);
  if (!handlerAndState || handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget) {
    handlerAndState = createProxyHandler(target, !!copiedTarget);
    handlerAndState[1][PROXY_PROPERTY] = newProxy(copiedTarget || target, handlerAndState[0]);
    if (proxyCache) {
      proxyCache.set(target, handlerAndState);
    }
  }
  handlerAndState[1][AFFECTED_PROPERTY] = affected;
  handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache;
  handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache;
  return handlerAndState[1][PROXY_PROPERTY];
};
var isAllOwnKeysChanged = (prevObj, nextObj) => {
  const prevKeys = Reflect.ownKeys(prevObj);
  const nextKeys = Reflect.ownKeys(nextObj);
  return prevKeys.length !== nextKeys.length || prevKeys.some((k, i) => k !== nextKeys[i]);
};
var isChanged = (prevObj, nextObj, affected, cache, isEqual = Object.is) => {
  if (isEqual(prevObj, nextObj)) {
    return false;
  }
  if (!isObject(prevObj) || !isObject(nextObj))
    return true;
  const used = affected.get(getOriginalObject(prevObj));
  if (!used)
    return true;
  if (cache) {
    const hit = cache.get(prevObj);
    if (hit === nextObj) {
      return false;
    }
    cache.set(prevObj, nextObj);
  }
  let changed = null;
  for (const key of used[HAS_KEY_PROPERTY] || []) {
    changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);
    if (changed)
      return changed;
  }
  if (used[ALL_OWN_KEYS_PROPERTY] === true) {
    changed = isAllOwnKeysChanged(prevObj, nextObj);
    if (changed)
      return changed;
  } else {
    for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {
      const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);
      const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);
      changed = hasPrev !== hasNext;
      if (changed)
        return changed;
    }
  }
  for (const key of used[KEYS_PROPERTY] || []) {
    changed = isChanged(prevObj[key], nextObj[key], affected, cache, isEqual);
    if (changed)
      return changed;
  }
  if (changed === null)
    throw new Error("invalid used");
  return changed;
};
var getUntracked = (obj) => {
  if (isObjectToTrack(obj)) {
    return obj[GET_ORIGINAL_SYMBOL] || null;
  }
  return null;
};
var affectedToPathList = (obj, affected, onlyWithValues) => {
  const list = [];
  const seen = /* @__PURE__ */ new WeakSet();
  const walk = (x, path) => {
    var _a, _b, _c;
    if (seen.has(x)) {
      return;
    }
    if (isObject(x)) {
      seen.add(x);
    }
    const used = isObject(x) && affected.get(getOriginalObject(x));
    if (used) {
      (_a = used[HAS_KEY_PROPERTY]) === null || _a === void 0 ? void 0 : _a.forEach((key) => {
        const segment = `:has(${String(key)})`;
        list.push(path ? [...path, segment] : [segment]);
      });
      if (used[ALL_OWN_KEYS_PROPERTY] === true) {
        const segment = ":ownKeys";
        list.push(path ? [...path, segment] : [segment]);
      } else {
        (_b = used[HAS_OWN_KEY_PROPERTY]) === null || _b === void 0 ? void 0 : _b.forEach((key) => {
          const segment = `:hasOwn(${String(key)})`;
          list.push(path ? [...path, segment] : [segment]);
        });
      }
      (_c = used[KEYS_PROPERTY]) === null || _c === void 0 ? void 0 : _c.forEach((key) => {
        if (!onlyWithValues || "value" in (Object.getOwnPropertyDescriptor(x, key) || {})) {
          walk(x[key], path ? [...path, key] : [key]);
        }
      });
    } else if (path) {
      list.push(path);
    }
  };
  walk(obj);
  return list;
};

// ../node_modules/.bun/react-tracked@2.0.1+9c2751b043b8213e/node_modules/react-tracked/dist/utils.js
var import_react2 = __toESM(require_react(), 1);
var useAffectedDebugValue = (state, affected) => {
  const pathList = (0, import_react2.useRef)();
  (0, import_react2.useEffect)(() => {
    pathList.current = affectedToPathList(state, affected);
  });
  (0, import_react2.useDebugValue)(state);
};

// ../node_modules/.bun/react-tracked@2.0.1+9c2751b043b8213e/node_modules/react-tracked/dist/createTrackedSelector.js
var hasGlobalProcess = typeof process === "object";
var createTrackedSelector = (useSelector) => {
  const useTrackedSelector = () => {
    const [, forceUpdate] = (0, import_react3.useReducer)((c) => c + 1, 0);
    const affected = (0, import_react3.useMemo)(() => /* @__PURE__ */ new WeakMap(), []);
    const prevState = (0, import_react3.useRef)();
    const lastState = (0, import_react3.useRef)();
    (0, import_react3.useEffect)(() => {
      if (prevState.current !== lastState.current && isChanged(prevState.current, lastState.current, affected, /* @__PURE__ */ new WeakMap())) {
        prevState.current = lastState.current;
        forceUpdate();
      }
    });
    const selector = (0, import_react3.useCallback)((nextState) => {
      lastState.current = nextState;
      if (prevState.current && prevState.current !== nextState && !isChanged(prevState.current, nextState, affected, /* @__PURE__ */ new WeakMap())) {
        return prevState.current;
      }
      prevState.current = nextState;
      return nextState;
    }, [affected]);
    const state = useSelector(selector);
    if (hasGlobalProcess && true) {
      useAffectedDebugValue(state, affected);
    }
    const proxyCache = (0, import_react3.useMemo)(() => /* @__PURE__ */ new WeakMap(), []);
    return createProxy(state, affected, proxyCache);
  };
  return useTrackedSelector;
};

// ../node_modules/.bun/react-tracked@2.0.1+9c2751b043b8213e/node_modules/react-tracked/dist/createContainer.js
var hasGlobalProcess2 = typeof process === "object";
var createContainer = (useValue, options) => {
  if (typeof options === "boolean") {
    console.warn("boolean option is deprecated, please specify { concurrentMode: true }");
    options = { concurrentMode: options };
  }
  const { stateContextName = "StateContainer", updateContextName = "UpdateContainer", concurrentMode } = options || {};
  const StateContext = createContext(options === null || options === void 0 ? void 0 : options.defaultState);
  const UpdateContext = (0, import_react4.createContext)(options === null || options === void 0 ? void 0 : options.defaultUpdate);
  StateContext.displayName = stateContextName;
  UpdateContext.displayName = updateContextName;
  const Provider = (props) => {
    const [state, update] = useValue(props);
    return (0, import_react4.createElement)(UpdateContext.Provider, { value: update }, (0, import_react4.createElement)(StateContext.Provider, { value: state }, props.children));
  };
  const useSelector = (selector) => {
    if (hasGlobalProcess2 && true) {
      const selectorOrig = selector;
      selector = (state) => {
        if (state === void 0) {
          throw new Error("Please use <Provider>");
        }
        return selectorOrig(state);
      };
    }
    const selected = useContextSelector(StateContext, selector);
    (0, import_react4.useDebugValue)(selected);
    return selected;
  };
  const useTrackedState = createTrackedSelector(useSelector);
  const useUpdate = concurrentMode ? () => {
    if (hasGlobalProcess2 && true && (0, import_react4.useContext)(UpdateContext) === void 0) {
      throw new Error("Please use <Provider>");
    }
    const contextUpdate = useContextUpdate(StateContext);
    const update = (0, import_react4.useContext)(UpdateContext);
    return (0, import_react4.useCallback)((...args) => {
      let result;
      contextUpdate(() => {
        result = update(...args);
      });
      return result;
    }, [contextUpdate, update]);
  } : (
    // not concurrentMode
    () => {
      if (typeof process === "object" && true && (0, import_react4.useContext)(UpdateContext) === void 0) {
        throw new Error("Please use <Provider>");
      }
      return (0, import_react4.useContext)(UpdateContext);
    }
  );
  const useTracked = () => [useTrackedState(), useUpdate()];
  return {
    Provider,
    useTrackedState,
    useTracked,
    useUpdate,
    useSelector
  };
};
export {
  createContainer,
  createTrackedSelector,
  getUntracked as getUntrackedObject
};
//# sourceMappingURL=react-tracked.js.map
