{"version": 3, "sources": ["../../../../node_modules/.bun/json-with-bigint@3.4.4/node_modules/json-with-bigint/json-with-bigint.js"], "sourcesContent": ["const noiseValue = /^-?\\d+n+$/; // Noise - strings that match the custom format before being converted to it\nconst originalStringify = JSON.stringify;\nconst originalParse = JSON.parse;\n\n/*\n  Function to serialize value to a JSON string.\n  Converts BigInt values to a custom format (strings with digits and \"n\" at the end) and then converts them to proper big integers in a JSON string.\n*/\nexport const JSONStringify = (value, replacer, space) => {\n  if (\"rawJSON\" in JSON) {\n    return originalStringify(\n      value,\n      (key, value) => {\n        if (typeof value === \"bigint\") return JSON.rawJSON(value.toString());\n\n        if (typeof replacer === \"function\") return replacer(key, value);\n\n        if (Array.isArray(replacer) && replacer.includes(key)) return value;\n\n        return value;\n      },\n      space\n    );\n  }\n\n  if (!value) return originalStringify(value, replacer, space);\n\n  const bigInts = /([\\[:])?\"(-?\\d+)n\"($|([\\\\n]|\\s)*(\\s|[\\\\n])*[,\\}\\]])/g;\n  const noise = /([\\[:])?(\"-?\\d+n+)n(\"$|\"([\\\\n]|\\s)*(\\s|[\\\\n])*[,\\}\\]])/g;\n  const convertedToCustomJSON = originalStringify(\n    value,\n    (key, value) => {\n      const isNoise =\n        typeof value === \"string\" && Boolean(value.match(noiseValue));\n\n      if (isNoise) return value.toString() + \"n\"; // Mark noise values with additional \"n\" to offset the deletion of one \"n\" during the processing\n\n      if (typeof value === \"bigint\") return value.toString() + \"n\";\n\n      if (typeof replacer === \"function\") return replacer(key, value);\n\n      if (Array.isArray(replacer) && replacer.includes(key)) return value;\n\n      return value;\n    },\n    space\n  );\n  const processedJSON = convertedToCustomJSON.replace(bigInts, \"$1$2$3\"); // Delete one \"n\" off the end of every BigInt value\n  const denoisedJSON = processedJSON.replace(noise, \"$1$2$3\"); // Remove one \"n\" off the end of every noisy string\n\n  return denoisedJSON;\n};\n\n/*\n  Function to parse JSON.\n  If JSON has number values greater than Number.MAX_SAFE_INTEGER, we convert those values to a custom format, then parse them to BigInt values.\n  Other types of values are not affected and parsed as native JSON.parse() would parse them.\n*/\nexport const JSONParse = (text, reviver) => {\n  if (!text) return originalParse(text, reviver);\n\n  const MAX_INT = Number.MAX_SAFE_INTEGER.toString();\n  const MAX_DIGITS = MAX_INT.length;\n  const stringsOrLargeNumbers =\n    /\"(?:\\\\.|[^\"])*\"|-?(0|[1-9][0-9]*)(\\.[0-9]+)?([eE][+-]?[0-9]+)?/g;\n  const noiseValueWithQuotes = /^\"-?\\d+n+\"$/; // Noise - strings that match the custom format before being converted to it\n  const customFormat = /^-?\\d+n$/;\n\n  // Find and mark big numbers with \"n\"\n  const serializedData = text.replace(\n    stringsOrLargeNumbers,\n    (text, digits, fractional, exponential) => {\n      const isString = text[0] === '\"';\n      const isNoise = isString && Boolean(text.match(noiseValueWithQuotes));\n\n      if (isNoise) return text.substring(0, text.length - 1) + 'n\"'; // Mark noise values with additional \"n\" to offset the deletion of one \"n\" during the processing\n\n      const isFractionalOrExponential = fractional || exponential;\n      const isLessThanMaxSafeInt =\n        digits &&\n        (digits.length < MAX_DIGITS ||\n          (digits.length === MAX_DIGITS && digits <= MAX_INT)); // With a fixed number of digits, we can correctly use lexicographical comparison to do a numeric comparison\n\n      if (isString || isFractionalOrExponential || isLessThanMaxSafeInt)\n        return text;\n\n      return '\"' + text + 'n\"';\n    }\n  );\n\n  // Convert marked big numbers to BigInt\n  return originalParse(serializedData, (key, value, context) => {\n    const isCustomFormatBigInt =\n      typeof value === \"string\" && Boolean(value.match(customFormat));\n\n    if (isCustomFormatBigInt)\n      return BigInt(value.substring(0, value.length - 1));\n\n    const isNoiseValue =\n      typeof value === \"string\" && Boolean(value.match(noiseValue));\n\n    if (isNoiseValue) return value.substring(0, value.length - 1); // Remove one \"n\" off the end of the noisy string\n\n    if (typeof reviver !== \"function\") return value;\n\n    return reviver(key, value, context);\n  });\n};\n"], "mappings": ";;;AAAA,IAAM,aAAa;AACnB,IAAM,oBAAoB,KAAK;AAC/B,IAAM,gBAAgB,KAAK;AAMpB,IAAM,gBAAgB,CAAC,OAAO,UAAU,UAAU;AACvD,MAAI,aAAa,MAAM;AACrB,WAAO;AAAA,MACL;AAAA,MACA,CAAC,KAAKA,WAAU;AACd,YAAI,OAAOA,WAAU,SAAU,QAAO,KAAK,QAAQA,OAAM,SAAS,CAAC;AAEnE,YAAI,OAAO,aAAa,WAAY,QAAO,SAAS,KAAKA,MAAK;AAE9D,YAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,EAAG,QAAOA;AAE9D,eAAOA;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,MAAO,QAAO,kBAAkB,OAAO,UAAU,KAAK;AAE3D,QAAM,UAAU;AAChB,QAAM,QAAQ;AACd,QAAM,wBAAwB;AAAA,IAC5B;AAAA,IACA,CAAC,KAAKA,WAAU;AACd,YAAM,UACJ,OAAOA,WAAU,YAAY,QAAQA,OAAM,MAAM,UAAU,CAAC;AAE9D,UAAI,QAAS,QAAOA,OAAM,SAAS,IAAI;AAEvC,UAAI,OAAOA,WAAU,SAAU,QAAOA,OAAM,SAAS,IAAI;AAEzD,UAAI,OAAO,aAAa,WAAY,QAAO,SAAS,KAAKA,MAAK;AAE9D,UAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,EAAG,QAAOA;AAE9D,aAAOA;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,QAAM,gBAAgB,sBAAsB,QAAQ,SAAS,QAAQ;AACrE,QAAM,eAAe,cAAc,QAAQ,OAAO,QAAQ;AAE1D,SAAO;AACT;AAOO,IAAM,YAAY,CAAC,MAAM,YAAY;AAC1C,MAAI,CAAC,KAAM,QAAO,cAAc,MAAM,OAAO;AAE7C,QAAM,UAAU,OAAO,iBAAiB,SAAS;AACjD,QAAM,aAAa,QAAQ;AAC3B,QAAM,wBACJ;AACF,QAAM,uBAAuB;AAC7B,QAAM,eAAe;AAGrB,QAAM,iBAAiB,KAAK;AAAA,IAC1B;AAAA,IACA,CAACC,OAAM,QAAQ,YAAY,gBAAgB;AACzC,YAAM,WAAWA,MAAK,CAAC,MAAM;AAC7B,YAAM,UAAU,YAAY,QAAQA,MAAK,MAAM,oBAAoB,CAAC;AAEpE,UAAI,QAAS,QAAOA,MAAK,UAAU,GAAGA,MAAK,SAAS,CAAC,IAAI;AAEzD,YAAM,4BAA4B,cAAc;AAChD,YAAM,uBACJ,WACC,OAAO,SAAS,cACd,OAAO,WAAW,cAAc,UAAU;AAE/C,UAAI,YAAY,6BAA6B;AAC3C,eAAOA;AAET,aAAO,MAAMA,QAAO;AAAA,IACtB;AAAA,EACF;AAGA,SAAO,cAAc,gBAAgB,CAAC,KAAK,OAAO,YAAY;AAC5D,UAAM,uBACJ,OAAO,UAAU,YAAY,QAAQ,MAAM,MAAM,YAAY,CAAC;AAEhE,QAAI;AACF,aAAO,OAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC;AAEpD,UAAM,eACJ,OAAO,UAAU,YAAY,QAAQ,MAAM,MAAM,UAAU,CAAC;AAE9D,QAAI,aAAc,QAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAE5D,QAAI,OAAO,YAAY,WAAY,QAAO;AAE1C,WAAO,QAAQ,KAAK,OAAO,OAAO;AAAA,EACpC,CAAC;AACH;", "names": ["value", "text"]}