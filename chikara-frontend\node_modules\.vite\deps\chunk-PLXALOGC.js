import {
  getEventMeta,
  withEventMeta
} from "./chunk-7CSQ75ER.js";
import {
  AsyncIteratorClass,
  isObject,
  isTypescriptObject
} from "./chunk-NUUPRKSZ.js";

// ../node_modules/.bun/@orpc+client@1.7.4/node_modules/@orpc/client/dist/shared/client.DHOfWE0c.mjs
var COMMON_ORPC_ERROR_DEFS = {
  BAD_REQUEST: {
    status: 400,
    message: "Bad Request"
  },
  UNAUTHORIZED: {
    status: 401,
    message: "Unauthorized"
  },
  FORBIDDEN: {
    status: 403,
    message: "Forbidden"
  },
  NOT_FOUND: {
    status: 404,
    message: "Not Found"
  },
  METHOD_NOT_SUPPORTED: {
    status: 405,
    message: "Method Not Supported"
  },
  NOT_ACCEPTABLE: {
    status: 406,
    message: "Not Acceptable"
  },
  TIMEOUT: {
    status: 408,
    message: "Request Timeout"
  },
  CONFLICT: {
    status: 409,
    message: "Conflict"
  },
  PRECONDITION_FAILED: {
    status: 412,
    message: "Precondition Failed"
  },
  PAYLOAD_TOO_LARGE: {
    status: 413,
    message: "Payload Too Large"
  },
  UNSUPPORTED_MEDIA_TYPE: {
    status: 415,
    message: "Unsupported Media Type"
  },
  UNPROCESSABLE_CONTENT: {
    status: 422,
    message: "Unprocessable Content"
  },
  TOO_MANY_REQUESTS: {
    status: 429,
    message: "Too Many Requests"
  },
  CLIENT_CLOSED_REQUEST: {
    status: 499,
    message: "Client Closed Request"
  },
  INTERNAL_SERVER_ERROR: {
    status: 500,
    message: "Internal Server Error"
  },
  NOT_IMPLEMENTED: {
    status: 501,
    message: "Not Implemented"
  },
  BAD_GATEWAY: {
    status: 502,
    message: "Bad Gateway"
  },
  SERVICE_UNAVAILABLE: {
    status: 503,
    message: "Service Unavailable"
  },
  GATEWAY_TIMEOUT: {
    status: 504,
    message: "Gateway Timeout"
  }
};
function fallbackORPCErrorStatus(code, status) {
  return status ?? COMMON_ORPC_ERROR_DEFS[code]?.status ?? 500;
}
function fallbackORPCErrorMessage(code, message) {
  return message || COMMON_ORPC_ERROR_DEFS[code]?.message || code;
}
var ORPCError = class extends Error {
  defined;
  code;
  status;
  data;
  constructor(code, ...[options]) {
    if (options?.status && !isORPCErrorStatus(options.status)) {
      throw new Error("[ORPCError] Invalid error status code.");
    }
    const message = fallbackORPCErrorMessage(code, options?.message);
    super(message, options);
    this.code = code;
    this.status = fallbackORPCErrorStatus(code, options?.status);
    this.defined = options?.defined ?? false;
    this.data = options?.data;
  }
  toJSON() {
    return {
      defined: this.defined,
      code: this.code,
      status: this.status,
      message: this.message,
      data: this.data
    };
  }
};
function isDefinedError(error) {
  return error instanceof ORPCError && error.defined;
}
function toORPCError(error) {
  return error instanceof ORPCError ? error : new ORPCError("INTERNAL_SERVER_ERROR", {
    message: "Internal server error",
    cause: error
  });
}
function isORPCErrorStatus(status) {
  return status < 200 || status >= 400;
}
function isORPCErrorJson(json) {
  if (!isObject(json)) {
    return false;
  }
  const validKeys = ["defined", "code", "status", "message", "data"];
  if (Object.keys(json).some((k) => !validKeys.includes(k))) {
    return false;
  }
  return "defined" in json && typeof json.defined === "boolean" && "code" in json && typeof json.code === "string" && "status" in json && typeof json.status === "number" && isORPCErrorStatus(json.status) && "message" in json && typeof json.message === "string";
}
function createORPCErrorFromJson(json, options = {}) {
  return new ORPCError(json.code, {
    ...options,
    ...json
  });
}
function mapEventIterator(iterator, maps) {
  return new AsyncIteratorClass(async () => {
    const { done, value } = await (async () => {
      try {
        return await iterator.next();
      } catch (error) {
        let mappedError = await maps.error(error);
        if (mappedError !== error) {
          const meta = getEventMeta(error);
          if (meta && isTypescriptObject(mappedError)) {
            mappedError = withEventMeta(mappedError, meta);
          }
        }
        throw mappedError;
      }
    })();
    let mappedValue = await maps.value(value, done);
    if (mappedValue !== value) {
      const meta = getEventMeta(value);
      if (meta && isTypescriptObject(mappedValue)) {
        mappedValue = withEventMeta(mappedValue, meta);
      }
    }
    return { done, value: mappedValue };
  }, async () => {
    await iterator.return?.();
  });
}

export {
  COMMON_ORPC_ERROR_DEFS,
  fallbackORPCErrorStatus,
  fallbackORPCErrorMessage,
  ORPCError,
  isDefinedError,
  toORPCError,
  isORPCErrorStatus,
  isORPCErrorJson,
  createORPCErrorFromJson,
  mapEventIterator
};
//# sourceMappingURL=chunk-PLXALOGC.js.map
