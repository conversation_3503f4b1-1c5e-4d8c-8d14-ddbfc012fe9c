"use client";
import {
  require_react
} from "./chunk-BUSN7M7O.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// ../node_modules/.bun/react-slot-counter@3.3.1+dfc90389513bb65c/node_modules/react-slot-counter/lib/index.esm.js
var import_react = __toESM(require_react());
var c = function() {
  return c = Object.assign || function(n2) {
    for (var e2, t2 = 1, r2 = arguments.length; t2 < r2; t2++) for (var i2 in e2 = arguments[t2]) Object.prototype.hasOwnProperty.call(e2, i2) && (n2[i2] = e2[i2]);
    return n2;
  }, c.apply(this, arguments);
};
var s = "undefined" != typeof window ? import_react.default.useLayoutEffect : import_react.default.useEffect;
var d = "index-module_slot_wrap__ZT-DX";
var f = "index-module_slot__DpPgW";
var m = "index-module_separator__7GhtE";
var v = "index-module_numbers__sqlqm";
var h = "index-module_num__j6XH3";
var p = "index-module_top_dummy_list__veRmf";
!function(n2, e2) {
  void 0 === e2 && (e2 = {});
  var t2 = e2.insertAt;
  if (n2 && "undefined" != typeof document) {
    var r2 = document.head || document.getElementsByTagName("head")[0], i2 = document.createElement("style");
    i2.type = "text/css", "top" === t2 && r2.firstChild ? r2.insertBefore(i2, r2.firstChild) : r2.appendChild(i2), i2.styleSheet ? i2.styleSheet.cssText = n2 : i2.appendChild(document.createTextNode(n2));
  }
}(".index-module_slot_wrap__ZT-DX {\n  display: inline-block;\n  white-space: nowrap;\n}\n\n.index-module_slot__DpPgW {\n  display: inline-block;\n  overflow: hidden;\n  text-align: center;\n  vertical-align: middle;\n  will-change: transform;\n}\n\n.index-module_separator__7GhtE {\n  display: inline-block;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.index-module_numbers__sqlqm {\n  display: block;\n}\n\n.index-module_num__j6XH3 {\n  display: block;\n}\n\n.index-module_top_dummy_list__veRmf {\n  position: absolute;\n  top: 0;\n  transform: translateY(-100%);\n}\n\n[aria-hidden=true] {\n  user-select: none;\n}");
var g = [",", ".", " "];
var y = /\u0020/;
var b = /\u00A0/;
var _ = /\u202F/;
var A = "slot-counter";
var N = "slot-counter-separator";
var C = "slot-counter-item";
var S = "slot-counter-item-numbers";
var x = "slot-counter-item-number";
var w = "slot-counter-item-number-value";
var E = function() {
  for (var n2 = [], e2 = 0; e2 < arguments.length; e2++) n2[e2] = arguments[e2];
  return n2.filter(Boolean).join(" ");
};
var M = function(n2, e2) {
  for (var t2 = [], r2 = n2; r2 < e2; r2 += 1) t2.push(r2);
  return t2;
};
var O = function(n2, e2) {
  for (var t2 = [], r2 = n2; r2 !== e2; ) t2.push(r2), 10 === (r2 += 1) && (r2 = 0);
  return t2;
};
var j = function(n2, e2) {
  var t2 = Math.random() * (e2 - n2);
  return Math.floor(t2 + n2);
};
var q = function(n2) {
  for (var e2, t2 = function(n3, e3, t3) {
    if (t3 || 2 === arguments.length) for (var r3, i3 = 0, o2 = e3.length; i3 < o2; i3++) !r3 && i3 in e3 || (r3 || (r3 = Array.prototype.slice.call(e3, 0, i3)), r3[i3] = e3[i3]);
    return n3.concat(r3 || Array.prototype.slice.call(e3));
  }([], n2, true), r2 = t2.length - 1; r2 > 0; r2 -= 1) {
    var i2 = Math.floor(Math.random() * (r2 + 1));
    e2 = [t2[i2], t2[r2]], t2[r2] = e2[0], t2[i2] = e2[1];
  }
  return t2;
};
var D = function(n2) {
  return "number" == typeof n2 ? n2 : (Array.isArray(n2) ? n2.join("") : n2).replace(/[,.]/g, "");
};
var k = function(n2) {
  return "object" != typeof n2 && !Number.isNaN(D(n2));
};
var T = function(n2) {
  return "object" == typeof n2;
};
var F = function(n2) {
  return !T(n2) && (g.includes(n2) || y.test(n2) || b.test(n2) || _.test(n2));
};
var I = (0, import_react.memo)((0, import_react.forwardRef)(function(e2, t2) {
  var l2, d2, m2 = e2.isNew, g2 = e2.charClassName, y2 = e2.numbersRef, b2 = e2.active, _2 = e2.isChanged, A2 = e2.effectiveDuration, N2 = e2.delay, M2 = e2.duration, O2 = e2.speed, j2 = e2.value, D2 = e2.startValue, k2 = e2.disableStartValue, T2 = e2.dummyList, F2 = e2.hasSequentialDummyList, I2 = e2.hasInfiniteList, L2 = e2.valueClassName, P = e2.numberSlotClassName, R = e2.numberClassName, W = e2.reverse, B = e2.sequentialAnimationMode, V = e2.useMonospaceWidth, Y = e2.maxNumberWidth, H = e2.onFontHeightChange, X = e2.slotPeek, z = (0, import_react.useState)(false), G = z[0], Z = z[1], J = (0, import_react.useState)(j2), U = J[0], K = J[1], Q = (0, import_react.useRef)(), $ = (0, import_react.useRef)(j2), nn = (0, import_react.useRef)(null), en = (0, import_react.useState)(0), tn = en[0], rn = en[1], on = (0, import_react.useState)(F2 ? T2 : q(T2)), un = on[0], an = on[1], ln = (0, import_react.useState)(false), cn = ln[0], sn = ln[1], dn = tn * (T2.length + 1);
  s(function() {
    sn(true);
  }, []), s(function() {
    var n2, e3;
    rn(null !== (e3 = null === (n2 = nn.current) || void 0 === n2 ? void 0 : n2.getBoundingClientRect().height) && void 0 !== e3 ? e3 : 0);
  }, [cn]), (0, import_react.useEffect)(function() {
    b2 ? requestAnimationFrame(function() {
      Z(b2);
    }) : Z(b2);
  }, [b2]), (0, import_react.useEffect)(function() {
    var n2 = nn.current;
    if (tn && n2 && "undefined" != typeof ResizeObserver) {
      var e3 = new ResizeObserver(function(n3) {
        var e4 = n3[0].contentRect.height;
        Math.abs(tn - e4) > 1 && (null == H || H(e4));
      });
      return e3.observe(n2), function() {
        e3.disconnect();
      };
    }
  }, [tn, H]), (0, import_react.useMemo)(function() {
    k2 && (Q.current = $.current);
  }, [k2]), (0, import_react.useEffect)(function() {
    G && (Q.current = $.current, $.current = j2, setTimeout(function() {
      return K(j2);
    }, B ? 0 : A2 * O2 * M2 * 1e3 / T2.length + 1e3 * N2));
  }, [G, j2, A2, N2, T2.length, B, O2, M2]), (0, import_react.useEffect)(function() {
    an(F2 ? T2 : q(T2));
  }, [T2, F2]), (0, import_react.useImperativeHandle)(t2, function() {
    return { refreshStyles: function() {
      var n2, e3;
      Z(true), rn(null !== (e3 = null === (n2 = nn.current) || void 0 === n2 ? void 0 : n2.getBoundingClientRect().height) && void 0 !== e3 ? e3 : 0), requestAnimationFrame(function() {
        Z(false);
      });
    } };
  });
  var fn = function() {
    return un.map(function(e3, t3) {
      return import_react.default.createElement("span", { key: t3, className: E(h, R, x), "aria-hidden": "true" }, e3);
    });
  }, mn = W ? U : null != D2 ? D2 : U;
  B && (mn = W ? U : null !== (l2 = null != D2 ? D2 : Q.current) && void 0 !== l2 ? l2 : U, B && m2 && !W && (mn = ""));
  var vn = W && null != D2 ? D2 : U;
  B && (vn = W && null !== (d2 = null != D2 ? D2 : Q.current) && void 0 !== d2 ? d2 : U);
  var hn = (0, import_react.useMemo)(function() {
    if (cn) return V ? Y : void 0;
  }, [cn, Y, V]), pn = (0, import_react.useMemo)(function() {
    if (cn) return X ? tn + 2 * X : tn;
  }, [cn, tn, X]);
  return import_react.default.createElement("span", { className: E(f, g2, C), style: c({ display: "inline-block", width: hn, height: pn }, X && { paddingTop: X, paddingBottom: X }) }, import_react.default.createElement("span", { ref: y2, className: E(v, P, S), style: c({ transition: "none", transform: W ? "translateY(-".concat(dn, "px)") : "translateY(0px)" }, G && _2 && { transform: W ? "translateY(0px)" : "translateY(-".concat(dn, "px)"), transition: "transform ".concat(A2, "s ").concat(N2, "s ease-in-out") }) }, cn ? import_react.default.createElement(import_react.default.Fragment, null, X && import_react.default.createElement("div", { className: p }, fn()), import_react.default.createElement("span", { className: E(h, R, x), "aria-hidden": "true", style: { height: tn } }, mn), fn(), import_react.default.createElement("span", { className: E(h, R, L2, x, w), ref: nn }, vn), I2 || X ? fn() : null) : import_react.default.createElement("span", { className: E(h, R, x), "aria-hidden": "true" }, null != D2 ? D2 : U)));
}));
var L = (0, import_react.memo)((0, import_react.forwardRef)(function(e2, t2) {
  var c2, f2, v2, h2, p2, g2, y2, b2, _2, S2, x2 = e2.value, w2 = e2.startValue, q2 = e2.startValueOnce, L2 = void 0 !== q2 && q2, P = e2.duration, R = void 0 === P ? 0.7 : P, W = e2.speed, B = void 0 === W ? 1.4 : W, V = e2.delay, Y = e2.dummyCharacters, H = e2.dummyCharacterCount, X = void 0 === H ? 6 : H, z = e2.autoAnimationStart, G = void 0 === z || z, Z = e2.containerClassName, J = e2.charClassName, U = e2.separatorClassName, K = e2.valueClassName, Q = e2.numberSlotClassName, $ = e2.numberClassName, nn = e2.animateUnchanged, en = void 0 !== nn && nn, tn = e2.hasInfiniteList, rn = void 0 !== tn && tn, on = e2.sequentialAnimationMode, un = void 0 !== on && on, an = e2.useMonospaceWidth, ln = void 0 !== an && an, cn = e2.direction, sn = e2.debounceDelay, dn = e2.animateOnVisible, fn = e2.startFromLastDigit, mn = void 0 !== fn && fn, vn = e2.onAnimationStart, hn = e2.onAnimationEnd, pn = e2.separatorCharacters, gn = e2.isSeparatorCharacter, yn = void 0 === gn ? F : gn, bn = e2.slotPeek, _n = function(n2, e3) {
    var t3 = (0, import_react.useState)(n2), i2 = t3[0], u2 = t3[1];
    return (0, import_react.useEffect)(function() {
      if (0 === e3) return u2(n2);
      var t4 = setTimeout(function() {
        u2(n2);
      }, e3);
      return function() {
        clearTimeout(t4);
      };
    }, [n2, e3]), i2;
  }(x2, null != sn ? sn : 0), An = (0, import_react.useMemo)(function() {
    return function(n2) {
      return Array.isArray(n2) && T(n2[0]);
    }(_n) ? "" : "object" == typeof _n ? JSON.stringify(_n) : _n.toString();
  }, [_n]), Nn = (0, import_react.useState)(false), Cn = Nn[0], Sn = Nn[1], xn = (0, import_react.useRef)(), wn = (0, import_react.useRef)(null), En = (0, import_react.useRef)(null), Mn = (0, import_react.useRef)(w2), On = (0, import_react.useRef)([]), jn = (0, import_react.useMemo)(function() {
    return "boolean" == typeof dn ? dn : "object" == typeof dn || void 0;
  }, [dn]), qn = (0, import_react.useMemo)(function() {
    return "object" == typeof dn ? dn.rootMargin : void 0;
  }, [dn]), Dn = (0, import_react.useMemo)(function() {
    return "object" == typeof dn ? dn.triggerOnce : void 0;
  }, [dn]), kn = (0, import_react.useRef)(true), Tn = !jn && G, Fn = (0, import_react.useRef)(null == w2 || Tn ? _n : w2), In = (0, import_react.useRef)(w2), Ln = (0, import_react.useRef)(0), Pn = (0, import_react.useRef)(0), Rn = (0, import_react.useState)([]), Wn = Rn[0], Bn = Rn[1], Vn = (0, import_react.useRef)(), Yn = (0, import_react.useState)(0), Hn = Yn[0], Xn = Yn[1], zn = (0, import_react.useState)(), Gn = zn[0], Zn = zn[1], Jn = (0, import_react.useRef)(false), Un = null != w2 && (!L2 || Ln.current < 1), Kn = null !== (f2 = null === (c2 = xn.current) || void 0 === c2 ? void 0 : c2.dummyCharacterCount) && void 0 !== f2 ? f2 : X, Qn = null !== (h2 = null === (v2 = xn.current) || void 0 === v2 ? void 0 : v2.duration) && void 0 !== h2 ? h2 : R, $n = (0, import_react.useRef)({ onAnimationStart: vn, onAnimationEnd: hn });
  $n.current = { onAnimationStart: vn, onAnimationEnd: hn };
  var ne = (0, import_react.useRef)(false), ee = (0, import_react.useCallback)(function() {
    var n2 = En.current;
    if (n2) {
      var e3 = M(0, 10).map(function(e4) {
        var t4 = document.createElement("span");
        t4.className = null != K ? K : "", t4.style.position = "absolute", t4.style.top = "0", t4.style.left = "-9999px", t4.style.visibility = "hidden", t4.textContent = e4.toString(), n2.appendChild(t4);
        var r2 = t4.getBoundingClientRect().width;
        return n2.removeChild(t4), r2;
      }), t3 = Math.max.apply(Math, e3);
      Zn(t3);
    }
  }, [K]);
  s(function() {
    var n2;
    ee(), null === (n2 = document.fonts) || void 0 === n2 || n2.ready.then(function() {
      ee();
    });
  }, []), (0, import_react.useEffect)(function() {
    Bn(M(0, Kn * R * B - 1).map(function(n2) {
      if (!Y) return j(0, 10);
      var e3 = n2 >= Y.length ? j(0, Y.length) : n2;
      return Y[e3];
    }));
  }, [Y, Kn, B, R]), Fn.current !== _n && Jn.current && Pn.current > 0 && (In.current = Fn.current, Fn.current = _n);
  var te = Array.isArray(In.current) ? In.current : null !== (g2 = null === (p2 = In.current) || void 0 === p2 ? void 0 : p2.toString().split("")) && void 0 !== g2 ? g2 : [], re = Array.isArray(Fn.current) ? Fn.current : null !== (b2 = null === (y2 = Fn.current) || void 0 === y2 ? void 0 : y2.toString().split("")) && void 0 !== b2 ? b2 : [], ie = Array.isArray(Mn.current) ? Mn.current : null !== (S2 = null === (_2 = Mn.current) || void 0 === _2 ? void 0 : _2.toString().split("")) && void 0 !== S2 ? S2 : [], oe = (0, import_react.useMemo)(function() {
    return Array.isArray(_n) ? _n : null == _n ? void 0 : _n.toString().split("");
  }, [_n]), ue = (0, import_react.useMemo)(function() {
    return Array.isArray(w2) ? w2 : null == w2 ? void 0 : w2.toString().split("");
  }, [w2]), ae = te.length !== re.length, le = [];
  re.forEach(function(n2, e3) {
    var t3 = re.length - e3 - 1, r2 = Un ? ie : te;
    (re[t3] !== r2[t3] || ae || en) && le.push(t3);
  }), mn || le.reverse();
  var ce = (0, import_react.useMemo)(function() {
    return V || Math.min(0.1, Qn / oe.length);
  }, [Qn, oe.length, V]), se = (0, import_react.useCallback)(function() {
    var n2, e3, t3;
    null === (e3 = (n2 = $n.current).onAnimationEnd) || void 0 === e3 || e3.call(n2), ne.current = false, null === (t3 = En.current) || void 0 === t3 || t3.removeEventListener("transitionend", se);
  }, []), de = (0, import_react.useCallback)(function() {
    var n2, e3, t3;
    Vn.current && window.cancelAnimationFrame(Vn.current), ne.current && se(), ne.current = true, null === (n2 = En.current) || void 0 === n2 || n2.addEventListener("transitionend", se), null === (t3 = (e3 = $n.current).onAnimationStart) || void 0 === t3 || t3.call(e3), Sn(false), Ln.current = Pn.current, Ln.current += 1, window.requestAnimationFrame(function() {
      var n3;
      null === (n3 = En.current) || void 0 === n3 || n3.offsetWidth, Vn.current = requestAnimationFrame(function() {
        Pn.current += 1, Sn(true);
      });
    });
  }, [se]), fe = (0, import_react.useCallback)(function(n2) {
    var e3, t3, r2 = Un ? w2 : In.current;
    if (null == r2 || !k(r2) || !k(_n)) return [];
    var i2 = r2.toString().length, o2 = _n.toString().length, u2 = i2 < o2, a2 = Math.abs(i2 - o2), l2 = Number(D(r2.toString())), c3 = Number(D(_n.toString())), s2 = Number(l2.toString()[u2 ? -a2 + n2 : a2 + n2] || 0), d2 = Number(c3.toString()[n2] || 0);
    if (d2 === s2) return [];
    var f3 = l2 < c3, m2 = f3 ? O((s2 + 1) % 10, d2) : O((d2 + 1) % 10, s2);
    return "bottom-up" !== (null !== (t3 = null === (e3 = xn.current) || void 0 === e3 ? void 0 : e3.direction) && void 0 !== t3 ? t3 : cn) || f3 ? m2 : m2.reverse();
  }, [Un, _n, w2, cn]), me = (0, import_react.useCallback)(function() {
    On.current.forEach(function(n2) {
      n2.refreshStyles();
    }), ee();
  }, [ee]);
  (0, import_react.useEffect)(function() {
    (Jn.current || null != In.current) && (Jn.current || null == Mn.current) && (Jn.current || Tn) && de();
  }, [An, de, Tn]), (0, import_react.useEffect)(function() {
    Tn && de();
  }, [Tn, de]), (0, import_react.useEffect)(function() {
    requestAnimationFrame(function() {
      Jn.current = true;
    });
  }, []), (0, import_react.useImperativeHandle)(t2, function() {
    return { startAnimation: _e, refreshStyles: me, reload: function() {
      return Xn(function(n2) {
        return n2 + 1;
      });
    } };
  });
  var ve = null == w2 || Tn || 0 !== Ln.current ? oe : ue || [], he = ((null == ue ? void 0 : ue.length) || 0) - ve.length, pe = function(n2) {
    var e3, t3 = (0, import_react.useRef)(n2), r2 = (0, import_react.useRef)(t3.current), u2 = n2.join(","), a2 = null === (e3 = t3.current) || void 0 === e3 ? void 0 : e3.join(",");
    return (0, import_react.useEffect)(function() {
      a2 !== u2 && (r2.current = (null == a2 ? void 0 : a2.split(",")) || [], t3.current = u2.split(","));
    }, [u2, a2]), { getPrevDependencies: (0, import_react.useCallback)(function() {
      return r2.current;
    }, []), setPrevDependenciesToSameAsCurrent: (0, import_react.useCallback)(function() {
      r2.current = t3.current;
    }, []) };
  }(ve), ge = pe.getPrevDependencies, ye = pe.setPrevDependenciesToSameAsCurrent, be = ve.length - ge().length, _e = (0, import_react.useCallback)(function(n2) {
    null == w2 || L2 || (In.current = void 0), xn.current = n2, de(), ye();
  }, [w2, L2, de, ye]), Ae = (0, import_react.useMemo)(function() {
    return /* @__PURE__ */ function(n2, e3) {
      var t3;
      return function() {
        for (var r2 = [], i2 = 0; i2 < arguments.length; i2++) r2[i2] = arguments[i2];
        clearTimeout(t3), t3 = setTimeout(function() {
          n2.apply(void 0, r2);
        }, e3);
      };
    }(function() {
      me();
    }, 0);
  }, [me]);
  (0, import_react.useEffect)(function() {
    if (wn.current && window.IntersectionObserver) {
      var n2 = new IntersectionObserver(function(n3) {
        n3[0].isIntersecting && me();
      });
      return n2.observe(wn.current), function() {
        return n2.disconnect();
      };
    }
  }, [me]), (0, import_react.useEffect)(function() {
    if (jn && wn.current && window.IntersectionObserver) {
      var n2 = new IntersectionObserver(function(t3) {
        t3[0].isIntersecting && kn.current && (_e(), kn.current = false, Dn && (n2.disconnect(), e3.disconnect()));
      }, { rootMargin: qn, threshold: 1 }), e3 = new IntersectionObserver(function(n3) {
        n3[0].isIntersecting || (kn.current = true);
      }, { threshold: 0 });
      return n2.observe(wn.current), e3.observe(wn.current), function() {
        n2.disconnect(), e3.disconnect();
      };
    }
  }, [jn, qn, Dn, _e]);
  var Ne = function(n2) {
    return pn && "string" == typeof n2 ? pn.includes(n2) : null !== yn && yn(n2);
  }, Ce = le.filter(function(n2) {
    return !Ne(ve[n2]);
  }), Se = -1;
  return import_react.default.createElement("span", { key: Hn, ref: wn, className: E(Z, d, A) }, ve.map(function(e3, t3) {
    var r2, i2, o2 = le.includes(t3), u2 = (o2 ? Ce.indexOf(t3) : 0) * ce, a2 = In.current, l2 = null != w2 && !!L2 && Ln.current > 1, c3 = null != _n && null != a2 && k(_n) && k(a2) && D(_n) < D(a2);
    if ((null === (r2 = xn.current) || void 0 === r2 ? void 0 : r2.direction) && (c3 = "top-down" === (null === (i2 = xn.current) || void 0 === i2 ? void 0 : i2.direction)), cn && (c3 = "top-down" === cn), Ne(e3)) return import_react.default.createElement("span", { key: re.length - t3 - 1, className: E(m, U, C, N) }, e3);
    var s2 = un && (!Tn || Pn.current > 1);
    return Se += 1, import_react.default.createElement(I, { key: ve.length - t3 - 1, index: t3, isNew: be > 0 && t3 < be, maxNumberWidth: Gn, numbersRef: En, active: Cn, isChanged: o2, charClassName: J, effectiveDuration: Qn, delay: u2, value: e3, startValue: l2 || null == ue ? void 0 : ue[t3 + he], disableStartValue: l2, dummyList: s2 ? fe(Se) : Wn, hasSequentialDummyList: s2, hasInfiniteList: rn, valueClassName: K, numberSlotClassName: Q, numberClassName: $, reverse: c3, sequentialAnimationMode: un, useMonospaceWidth: ln, onFontHeightChange: Ae, speed: B, duration: R, slotPeek: bn, ref: function(n2) {
      n2 && On.current.push(n2);
    } });
  }));
}));
export {
  L as default
};
//# sourceMappingURL=react-slot-counter.js.map
