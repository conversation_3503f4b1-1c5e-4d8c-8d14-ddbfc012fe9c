{"version": 3, "sources": ["../../../../node_modules/.bun/react-string-replace@1.1.1/node_modules/react-string-replace/index.js"], "sourcesContent": ["/* eslint-disable vars-on-top, no-var, prefer-template */\nvar isRegExp = function (re) { \n  return re instanceof RegExp;\n};\nvar escapeRegExp = function escapeRegExp(string) {\n  var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g,\n    reHasRegExpChar = RegExp(reRegExpChar.source);\n\n  return (string && reHasRegExpChar.test(string))\n    ? string.replace(reRegExpChar, '\\\\$&')\n    : string;\n};\nvar isString = function (value) {\n  return typeof value === 'string';\n};\nvar flatten = function (array) {\n  var newArray = [];\n\n  array.forEach(function (item) {\n    if (Array.isArray(item)) {\n      newArray = newArray.concat(item);\n    } else {\n      newArray.push(item);\n    }\n  });\n\n  return newArray;\n};\n\n/**\n * Given a string, replace every substring that is matched by the `match` regex\n * with the result of calling `fn` on matched substring. The result will be an\n * array with all odd indexed elements containing the replacements. The primary\n * use case is similar to using String.prototype.replace except for React.\n *\n * React will happily render an array as children of a react element, which\n * makes this approach very useful for tasks like surrounding certain text\n * within a string with react elements.\n *\n * Example:\n * matchReplace(\n *   'Emphasize all phone numbers like ************.',\n *   /([\\d|-]+)/g,\n *   (number, i) => <strong key={i}>{number}</strong>\n * );\n * // => ['Emphasize all phone numbers like ', <strong>************</strong>, '.'\n *\n * @param {string} str\n * @param {RegExp|str} match Must contain a matching group\n * @param {function} fn\n * @return {array}\n */\nfunction replaceString(str, match, fn) {\n  var curCharStart = 0;\n  var curCharLen = 0;\n\n  if (str === '') {\n    return str;\n  } else if (!str || !isString(str)) {\n    throw new TypeError('First argument to react-string-replace#replaceString must be a string');\n  }\n\n  var re = match;\n\n  if (!isRegExp(re)) {\n    re = new RegExp('(' + escapeRegExp(re) + ')', 'gi');\n  }\n\n  var result = str.split(re);\n\n  // Apply fn to all odd elements\n  for (var i = 1, length = result.length; i < length; i += 2) {\n    /** @see {@link https://github.com/iansinnott/react-string-replace/issues/74} */\n    if (result[i] === undefined || result[i - 1] === undefined) {\n      console.warn('reactStringReplace: Encountered undefined value during string replacement. Your RegExp may not be working the way you expect.');\n      continue;\n    }\n\n    curCharLen = result[i].length;\n    curCharStart += result[i - 1].length;\n    result[i] = fn(result[i], i, curCharStart);\n    curCharStart += curCharLen;\n  }\n\n  return result;\n}\n\nmodule.exports = function reactStringReplace(source, match, fn) {\n  if (!Array.isArray(source)) source = [source];\n\n  return flatten(source.map(function(x) {\n    return isString(x) ? replaceString(x, match, fn) : x;\n  }));\n};\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,WAAW,SAAU,IAAI;AAC3B,aAAO,cAAc;AAAA,IACvB;AACA,QAAI,eAAe,SAASA,cAAa,QAAQ;AAC/C,UAAI,eAAe,uBACjB,kBAAkB,OAAO,aAAa,MAAM;AAE9C,aAAQ,UAAU,gBAAgB,KAAK,MAAM,IACzC,OAAO,QAAQ,cAAc,MAAM,IACnC;AAAA,IACN;AACA,QAAI,WAAW,SAAU,OAAO;AAC9B,aAAO,OAAO,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,SAAU,OAAO;AAC7B,UAAI,WAAW,CAAC;AAEhB,YAAM,QAAQ,SAAU,MAAM;AAC5B,YAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,qBAAW,SAAS,OAAO,IAAI;AAAA,QACjC,OAAO;AACL,mBAAS,KAAK,IAAI;AAAA,QACpB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAyBA,aAAS,cAAc,KAAK,OAAO,IAAI;AACrC,UAAI,eAAe;AACnB,UAAI,aAAa;AAEjB,UAAI,QAAQ,IAAI;AACd,eAAO;AAAA,MACT,WAAW,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC7F;AAEA,UAAI,KAAK;AAET,UAAI,CAAC,SAAS,EAAE,GAAG;AACjB,aAAK,IAAI,OAAO,MAAM,aAAa,EAAE,IAAI,KAAK,IAAI;AAAA,MACpD;AAEA,UAAI,SAAS,IAAI,MAAM,EAAE;AAGzB,eAAS,IAAI,GAAG,SAAS,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AAE1D,YAAI,OAAO,CAAC,MAAM,UAAa,OAAO,IAAI,CAAC,MAAM,QAAW;AAC1D,kBAAQ,KAAK,+HAA+H;AAC5I;AAAA,QACF;AAEA,qBAAa,OAAO,CAAC,EAAE;AACvB,wBAAgB,OAAO,IAAI,CAAC,EAAE;AAC9B,eAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,YAAY;AACzC,wBAAgB;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,mBAAmB,QAAQ,OAAO,IAAI;AAC9D,UAAI,CAAC,MAAM,QAAQ,MAAM,EAAG,UAAS,CAAC,MAAM;AAE5C,aAAO,QAAQ,OAAO,IAAI,SAAS,GAAG;AACpC,eAAO,SAAS,CAAC,IAAI,cAAc,GAAG,OAAO,EAAE,IAAI;AAAA,MACrD,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA;", "names": ["escapeRegExp"]}