{"version": 3, "sources": ["../../../../node_modules/.bun/@orpc+standard-server@1.7.4/node_modules/@orpc/standard-server/dist/batch/index.mjs", "../../../../node_modules/.bun/@orpc+client@1.7.4/node_modules/@orpc/client/dist/plugins/index.mjs"], "sourcesContent": ["import { stringifyJSO<PERSON>, parseEmptyableJSON, isAsyncIteratorObject, isObject } from '@orpc/shared';\n\nfunction toBatchAbortSignal(signals) {\n  const realSignals = signals.filter((signal) => signal !== void 0);\n  if (realSignals.length === 0 || realSignals.length !== signals.length) {\n    return void 0;\n  }\n  const controller = new AbortController();\n  const abortIfAllInputsAborted = () => {\n    if (realSignals.every((signal) => signal.aborted)) {\n      controller.abort();\n    }\n  };\n  abortIfAllInputsAborted();\n  for (const signal of realSignals) {\n    signal.addEventListener(\"abort\", () => {\n      abortIfAllInputsAborted();\n    }, {\n      once: true,\n      signal: controller.signal\n    });\n  }\n  return controller.signal;\n}\n\nfunction toBatchRequest(options) {\n  const url = new URL(options.url);\n  let body;\n  const batchRequestItems = options.requests.map((request) => ({\n    body: request.body,\n    headers: Object.keys(request.headers).length ? request.headers : void 0,\n    method: request.method === options.method ? void 0 : request.method,\n    url: request.url\n  }));\n  if (options.method === \"GET\") {\n    url.searchParams.append(\"batch\", stringifyJSON(batchRequestItems));\n  } else if (options.method === \"POST\") {\n    body = batchRequestItems;\n  }\n  return {\n    method: options.method,\n    url,\n    headers: options.headers,\n    body,\n    signal: toBatchAbortSignal(options.requests.map((request) => request.signal))\n  };\n}\nfunction parseBatchRequest(request) {\n  const items = request.method === \"GET\" ? parseEmptyableJSON(request.url.searchParams.getAll(\"batch\").at(-1)) : request.body;\n  if (!Array.isArray(items)) {\n    throw new TypeError(\"Invalid batch request\");\n  }\n  return items.map((item) => {\n    return {\n      method: item.method ?? request.method,\n      url: new URL(item.url),\n      headers: item.headers ?? {},\n      body: item.body,\n      signal: request.signal\n    };\n  });\n}\n\nfunction toBatchResponse(options) {\n  const mode = options.mode ?? \"streaming\";\n  const minifyResponseItem = (item) => {\n    return {\n      index: item.index,\n      status: item.status === options.status ? void 0 : item.status,\n      headers: Object.keys(item.headers).length ? item.headers : void 0,\n      body: item.body\n    };\n  };\n  if (mode === \"buffered\") {\n    return (async () => {\n      try {\n        const body = [];\n        for await (const item of options.body) {\n          body.push(minifyResponseItem(item));\n        }\n        return {\n          headers: options.headers,\n          status: options.status,\n          body\n        };\n      } finally {\n        await options.body.return?.();\n      }\n    })();\n  }\n  return {\n    headers: options.headers,\n    status: options.status,\n    body: async function* () {\n      try {\n        for await (const item of options.body) {\n          yield {\n            index: item.index,\n            status: item.status === options.status ? void 0 : item.status,\n            headers: Object.keys(item.headers).length ? item.headers : void 0,\n            body: item.body\n          };\n        }\n      } finally {\n        await options.body.return?.();\n      }\n    }()\n  };\n}\nfunction parseBatchResponse(response) {\n  const body = response.body;\n  if (isAsyncIteratorObject(body) || Array.isArray(body)) {\n    return async function* () {\n      try {\n        for await (const item of body) {\n          if (!isObject(item) || !(\"index\" in item) || typeof item.index !== \"number\") {\n            throw new TypeError(\"Invalid batch response\", {\n              cause: item\n            });\n          }\n          yield {\n            index: item.index,\n            status: item.status ?? response.status,\n            headers: item.headers ?? {},\n            body: item.body\n          };\n        }\n      } finally {\n        if (isAsyncIteratorObject(body)) {\n          await body.return?.();\n        }\n      }\n    }();\n  }\n  throw new TypeError(\"Invalid batch response\", {\n    cause: response\n  });\n}\n\nexport { parseBatchRequest, parseBatchResponse, toBatchAbortSignal, toBatchRequest, toBatchResponse };\n", "import { isAsyncIteratorObject, defer, value, splitInHalf, toArray, stringifyJSON } from '@orpc/shared';\nimport { toBatchRequest, parseBatchResponse, toBatchAbortSignal } from '@orpc/standard-server/batch';\nimport { replicateStandardLazyResponse, getEventMeta } from '@orpc/standard-server';\n\nclass BatchLinkPlugin {\n  groups;\n  maxSize;\n  batchUrl;\n  maxUrlLength;\n  batchHeaders;\n  mapRequestItem;\n  exclude;\n  mode;\n  pending;\n  order = 5e6;\n  constructor(options) {\n    this.groups = options.groups;\n    this.pending = /* @__PURE__ */ new Map();\n    this.maxSize = options.maxSize ?? 10;\n    this.maxUrlLength = options.maxUrlLength ?? 2083;\n    this.mode = options.mode ?? \"streaming\";\n    this.batchUrl = options.url ?? (([options2]) => `${options2.request.url.origin}${options2.request.url.pathname}/__batch__`);\n    this.batchHeaders = options.headers ?? (([options2, ...rest]) => {\n      const headers = {};\n      for (const [key, value2] of Object.entries(options2.request.headers)) {\n        if (rest.every((item) => item.request.headers[key] === value2)) {\n          headers[key] = value2;\n        }\n      }\n      return headers;\n    });\n    this.mapRequestItem = options.mapRequestItem ?? (({ request, batchHeaders }) => {\n      const headers = {};\n      for (const [key, value2] of Object.entries(request.headers)) {\n        if (batchHeaders[key] !== value2) {\n          headers[key] = value2;\n        }\n      }\n      return {\n        method: request.method,\n        url: request.url,\n        headers,\n        body: request.body,\n        signal: request.signal\n      };\n    });\n    this.exclude = options.exclude ?? (() => false);\n  }\n  init(options) {\n    options.clientInterceptors ??= [];\n    options.clientInterceptors.push((options2) => {\n      if (options2.request.headers[\"x-orpc-batch\"] !== \"1\") {\n        return options2.next();\n      }\n      return options2.next({\n        ...options2,\n        request: {\n          ...options2.request,\n          headers: {\n            ...options2.request.headers,\n            \"x-orpc-batch\": void 0\n          }\n        }\n      });\n    });\n    options.clientInterceptors.push((options2) => {\n      if (this.exclude(options2) || options2.request.body instanceof Blob || options2.request.body instanceof FormData || isAsyncIteratorObject(options2.request.body)) {\n        return options2.next();\n      }\n      const group = this.groups.find((group2) => group2.condition(options2));\n      if (!group) {\n        return options2.next();\n      }\n      return new Promise((resolve, reject) => {\n        this.#enqueueRequest(group, options2, resolve, reject);\n        defer(() => this.#processPendingBatches());\n      });\n    });\n  }\n  #enqueueRequest(group, options, resolve, reject) {\n    const items = this.pending.get(group);\n    if (items) {\n      items.push([options, resolve, reject]);\n    } else {\n      this.pending.set(group, [[options, resolve, reject]]);\n    }\n  }\n  async #processPendingBatches() {\n    const pending = this.pending;\n    this.pending = /* @__PURE__ */ new Map();\n    for (const [group, items] of pending) {\n      const getItems = items.filter(([options]) => options.request.method === \"GET\");\n      const restItems = items.filter(([options]) => options.request.method !== \"GET\");\n      this.#executeBatch(\"GET\", group, getItems);\n      this.#executeBatch(\"POST\", group, restItems);\n    }\n  }\n  async #executeBatch(method, group, groupItems) {\n    if (!groupItems.length) {\n      return;\n    }\n    const batchItems = groupItems;\n    if (batchItems.length === 1) {\n      batchItems[0][0].next().then(batchItems[0][1]).catch(batchItems[0][2]);\n      return;\n    }\n    try {\n      const options = batchItems.map(([options2]) => options2);\n      const maxSize = await value(this.maxSize, options);\n      if (batchItems.length > maxSize) {\n        const [first, second] = splitInHalf(batchItems);\n        this.#executeBatch(method, group, first);\n        this.#executeBatch(method, group, second);\n        return;\n      }\n      const batchUrl = new URL(await value(this.batchUrl, options));\n      const batchHeaders = await value(this.batchHeaders, options);\n      const mappedItems = batchItems.map(([options2]) => this.mapRequestItem({ ...options2, batchUrl, batchHeaders }));\n      const batchRequest = toBatchRequest({\n        method,\n        url: batchUrl,\n        headers: batchHeaders,\n        requests: mappedItems\n      });\n      const maxUrlLength = await value(this.maxUrlLength, options);\n      if (batchRequest.url.toString().length > maxUrlLength) {\n        const [first, second] = splitInHalf(batchItems);\n        this.#executeBatch(method, group, first);\n        this.#executeBatch(method, group, second);\n        return;\n      }\n      const mode = value(this.mode, options);\n      const lazyResponse = await options[0].next({\n        request: { ...batchRequest, headers: { ...batchRequest.headers, \"x-orpc-batch\": mode } },\n        signal: batchRequest.signal,\n        context: group.context,\n        input: group.input,\n        path: toArray(group.path)\n      });\n      const parsed = parseBatchResponse({ ...lazyResponse, body: await lazyResponse.body() });\n      for await (const item of parsed) {\n        batchItems[item.index]?.[1]({ ...item, body: () => Promise.resolve(item.body) });\n      }\n      throw new Error(\"Something went wrong make batch response not contains enough responses. This can be a bug please report it.\");\n    } catch (error) {\n      for (const [, , reject] of batchItems) {\n        reject(error);\n      }\n    }\n  }\n}\n\nclass DedupeRequestsPlugin {\n  #groups;\n  #filter;\n  order = 4e6;\n  // make sure execute before batch plugin\n  #queue = /* @__PURE__ */ new Map();\n  constructor(options) {\n    this.#groups = options.groups;\n    this.#filter = options.filter ?? (({ request }) => request.method === \"GET\");\n  }\n  init(options) {\n    options.clientInterceptors ??= [];\n    options.clientInterceptors.push((options2) => {\n      if (options2.request.body instanceof Blob || options2.request.body instanceof FormData || options2.request.body instanceof URLSearchParams || isAsyncIteratorObject(options2.request.body) || !this.#filter(options2)) {\n        return options2.next();\n      }\n      const group = this.#groups.find((group2) => group2.condition(options2));\n      if (!group) {\n        return options2.next();\n      }\n      return new Promise((resolve, reject) => {\n        this.#enqueue(group, options2, resolve, reject);\n        defer(() => this.#dequeue());\n      });\n    });\n  }\n  #enqueue(group, options, resolve, reject) {\n    let queue = this.#queue.get(group);\n    if (!queue) {\n      this.#queue.set(group, queue = []);\n    }\n    const matched = queue.find((item) => {\n      const requestString1 = stringifyJSON({\n        body: item.options.request.body,\n        headers: item.options.request.headers,\n        method: item.options.request.method,\n        url: item.options.request.url\n      });\n      const requestString2 = stringifyJSON({\n        body: options.request.body,\n        headers: options.request.headers,\n        method: options.request.method,\n        url: options.request.url\n      });\n      return requestString1 === requestString2;\n    });\n    if (matched) {\n      matched.signals.push(options.request.signal);\n      matched.resolves.push(resolve);\n      matched.rejects.push(reject);\n    } else {\n      queue.push({\n        options,\n        signals: [options.request.signal],\n        resolves: [resolve],\n        rejects: [reject]\n      });\n    }\n  }\n  async #dequeue() {\n    const promises = [];\n    for (const [group, items] of this.#queue) {\n      for (const { options, signals, resolves, rejects } of items) {\n        promises.push(\n          this.#execute(group, options, signals, resolves, rejects)\n        );\n      }\n    }\n    this.#queue.clear();\n    await Promise.all(promises);\n  }\n  async #execute(group, options, signals, resolves, rejects) {\n    try {\n      const dedupedRequest = {\n        ...options.request,\n        signal: toBatchAbortSignal(signals)\n      };\n      const response = await options.next({\n        ...options,\n        request: dedupedRequest,\n        signal: dedupedRequest.signal,\n        context: group.context\n      });\n      const replicatedResponses = replicateStandardLazyResponse(response, resolves.length);\n      for (const resolve of resolves) {\n        resolve(replicatedResponses.shift());\n      }\n    } catch (error) {\n      for (const reject of rejects) {\n        reject(error);\n      }\n    }\n  }\n}\n\nclass ClientRetryPluginInvalidEventIteratorRetryResponse extends Error {\n}\nclass ClientRetryPlugin {\n  defaultRetry;\n  defaultRetryDelay;\n  defaultShouldRetry;\n  defaultOnRetry;\n  constructor(options = {}) {\n    this.defaultRetry = options.default?.retry ?? 0;\n    this.defaultRetryDelay = options.default?.retryDelay ?? ((o) => o.lastEventRetry ?? 2e3);\n    this.defaultShouldRetry = options.default?.shouldRetry ?? true;\n    this.defaultOnRetry = options.default?.onRetry;\n  }\n  init(options) {\n    options.interceptors ??= [];\n    options.interceptors.push(async (interceptorOptions) => {\n      const maxAttempts = await value(\n        interceptorOptions.context.retry ?? this.defaultRetry,\n        interceptorOptions\n      );\n      const retryDelay = interceptorOptions.context.retryDelay ?? this.defaultRetryDelay;\n      const shouldRetry = interceptorOptions.context.shouldRetry ?? this.defaultShouldRetry;\n      const onRetry = interceptorOptions.context.onRetry ?? this.defaultOnRetry;\n      if (maxAttempts <= 0) {\n        return interceptorOptions.next();\n      }\n      let lastEventId = interceptorOptions.lastEventId;\n      let lastEventRetry;\n      let callback;\n      let attemptIndex = 0;\n      const next = async (initialError) => {\n        let currentError = initialError;\n        while (true) {\n          const updatedInterceptorOptions = { ...interceptorOptions, lastEventId };\n          if (currentError) {\n            if (attemptIndex >= maxAttempts) {\n              throw currentError.error;\n            }\n            const attemptOptions = {\n              ...updatedInterceptorOptions,\n              attemptIndex,\n              error: currentError.error,\n              lastEventRetry\n            };\n            const shouldRetryBool = await value(\n              shouldRetry,\n              attemptOptions\n            );\n            if (!shouldRetryBool) {\n              throw currentError.error;\n            }\n            callback = onRetry?.(attemptOptions);\n            const retryDelayMs = await value(retryDelay, attemptOptions);\n            await new Promise((resolve) => setTimeout(resolve, retryDelayMs));\n            attemptIndex++;\n          }\n          try {\n            currentError = void 0;\n            return await interceptorOptions.next(updatedInterceptorOptions);\n          } catch (error) {\n            currentError = { error };\n            if (updatedInterceptorOptions.signal?.aborted === true) {\n              throw error;\n            }\n          } finally {\n            callback?.(!currentError);\n            callback = void 0;\n          }\n        }\n      };\n      const output = await next();\n      if (!isAsyncIteratorObject(output)) {\n        return output;\n      }\n      return async function* () {\n        let current = output;\n        try {\n          while (true) {\n            try {\n              const item = await current.next();\n              const meta = getEventMeta(item.value);\n              lastEventId = meta?.id ?? lastEventId;\n              lastEventRetry = meta?.retry ?? lastEventRetry;\n              if (item.done) {\n                return item.value;\n              }\n              yield item.value;\n            } catch (error) {\n              const meta = getEventMeta(error);\n              lastEventId = meta?.id ?? lastEventId;\n              lastEventRetry = meta?.retry ?? lastEventRetry;\n              const maybeEventIterator = await next({ error });\n              if (!isAsyncIteratorObject(maybeEventIterator)) {\n                throw new ClientRetryPluginInvalidEventIteratorRetryResponse(\n                  \"RetryPlugin: Expected an Event Iterator, got a non-Event Iterator\"\n                );\n              }\n              current = maybeEventIterator;\n            }\n          }\n        } finally {\n          await current.return?.();\n        }\n      }();\n    });\n  }\n}\n\nclass SimpleCsrfProtectionLinkPlugin {\n  headerName;\n  headerValue;\n  exclude;\n  constructor(options = {}) {\n    this.headerName = options.headerName ?? \"x-csrf-token\";\n    this.headerValue = options.headerValue ?? \"orpc\";\n    this.exclude = options.exclude ?? false;\n  }\n  order = 8e6;\n  init(options) {\n    options.clientInterceptors ??= [];\n    options.clientInterceptors.push(async (options2) => {\n      const excluded = await value(this.exclude, options2);\n      if (excluded) {\n        return options2.next();\n      }\n      const headerName = await value(this.headerName, options2);\n      const headerValue = await value(this.headerValue, options2);\n      return options2.next({\n        ...options2,\n        request: {\n          ...options2.request,\n          headers: {\n            ...options2.request.headers,\n            [headerName]: headerValue\n          }\n        }\n      });\n    });\n  }\n}\n\nexport { BatchLinkPlugin, ClientRetryPlugin, ClientRetryPluginInvalidEventIteratorRetryResponse, DedupeRequestsPlugin, SimpleCsrfProtectionLinkPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;AAEA,SAAS,mBAAmB,SAAS;AACnC,QAAM,cAAc,QAAQ,OAAO,CAAC,WAAW,WAAW,MAAM;AAChE,MAAI,YAAY,WAAW,KAAK,YAAY,WAAW,QAAQ,QAAQ;AACrE,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,gBAAgB;AACvC,QAAM,0BAA0B,MAAM;AACpC,QAAI,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO,GAAG;AACjD,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AACA,0BAAwB;AACxB,aAAW,UAAU,aAAa;AAChC,WAAO,iBAAiB,SAAS,MAAM;AACrC,8BAAwB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,MACN,QAAQ,WAAW;AAAA,IACrB,CAAC;AAAA,EACH;AACA,SAAO,WAAW;AACpB;AAEA,SAAS,eAAe,SAAS;AAC/B,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,MAAI;AACJ,QAAM,oBAAoB,QAAQ,SAAS,IAAI,CAAC,aAAa;AAAA,IAC3D,MAAM,QAAQ;AAAA,IACd,SAAS,OAAO,KAAK,QAAQ,OAAO,EAAE,SAAS,QAAQ,UAAU;AAAA,IACjE,QAAQ,QAAQ,WAAW,QAAQ,SAAS,SAAS,QAAQ;AAAA,IAC7D,KAAK,QAAQ;AAAA,EACf,EAAE;AACF,MAAI,QAAQ,WAAW,OAAO;AAC5B,QAAI,aAAa,OAAO,SAAS,cAAc,iBAAiB,CAAC;AAAA,EACnE,WAAW,QAAQ,WAAW,QAAQ;AACpC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,QAAQ,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS,QAAQ;AAAA,IACjB;AAAA,IACA,QAAQ,mBAAmB,QAAQ,SAAS,IAAI,CAAC,YAAY,QAAQ,MAAM,CAAC;AAAA,EAC9E;AACF;AA+DA,SAAS,mBAAmB,UAAU;AACpC,QAAM,OAAO,SAAS;AACtB,MAAI,sBAAsB,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG;AACtD,WAAO,mBAAmB;AACxB,UAAI;AACF,yBAAiB,QAAQ,MAAM;AAC7B,cAAI,CAAC,SAAS,IAAI,KAAK,EAAE,WAAW,SAAS,OAAO,KAAK,UAAU,UAAU;AAC3E,kBAAM,IAAI,UAAU,0BAA0B;AAAA,cAC5C,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AACA,gBAAM;AAAA,YACJ,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK,UAAU,SAAS;AAAA,YAChC,SAAS,KAAK,WAAW,CAAC;AAAA,YAC1B,MAAM,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF,UAAE;AACA,YAAI,sBAAsB,IAAI,GAAG;AAC/B,gBAAM,KAAK,SAAS;AAAA,QACtB;AAAA,MACF;AAAA,IACF,EAAE;AAAA,EACJ;AACA,QAAM,IAAI,UAAU,0BAA0B;AAAA,IAC5C,OAAO;AAAA,EACT,CAAC;AACH;;;ACrIA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,SAAS;AACnB,SAAK,SAAS,QAAQ;AACtB,SAAK,UAA0B,oBAAI,IAAI;AACvC,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,eAAe,QAAQ,gBAAgB;AAC5C,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,WAAW,QAAQ,QAAQ,CAAC,CAAC,QAAQ,MAAM,GAAG,SAAS,QAAQ,IAAI,MAAM,GAAG,SAAS,QAAQ,IAAI,QAAQ;AAC9G,SAAK,eAAe,QAAQ,YAAY,CAAC,CAAC,UAAa,OAAI,MAAM;AAC/D,YAAM,UAAU,CAAC;AACjB,iBAAW,CAAC,KAAK,MAAM,KAAK,OAAO,QAAQ,SAAS,QAAQ,OAAO,GAAG;AACpE,YAAI,KAAK,MAAM,CAAC,SAAS,KAAK,QAAQ,QAAQ,GAAG,MAAM,MAAM,GAAG;AAC9D,kBAAQ,GAAG,IAAI;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,iBAAiB,QAAQ,mBAAmB,CAAC,EAAE,SAAS,aAAa,MAAM;AAC9E,YAAM,UAAU,CAAC;AACjB,iBAAW,CAAC,KAAK,MAAM,KAAK,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAC3D,YAAI,aAAa,GAAG,MAAM,QAAQ;AAChC,kBAAQ,GAAG,IAAI;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,QACL,QAAQ,QAAQ;AAAA,QAChB,KAAK,QAAQ;AAAA,QACb;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,QAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AACA,SAAK,UAAU,QAAQ,YAAY,MAAM;AAAA,EAC3C;AAAA,EACA,KAAK,SAAS;AACZ,YAAQ,uBAAuB,CAAC;AAChC,YAAQ,mBAAmB,KAAK,CAAC,aAAa;AAC5C,UAAI,SAAS,QAAQ,QAAQ,cAAc,MAAM,KAAK;AACpD,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,aAAO,SAAS,KAAK;AAAA,QACnB,GAAG;AAAA,QACH,SAAS;AAAA,UACP,GAAG,SAAS;AAAA,UACZ,SAAS;AAAA,YACP,GAAG,SAAS,QAAQ;AAAA,YACpB,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,YAAQ,mBAAmB,KAAK,CAAC,aAAa;AAC5C,UAAI,KAAK,QAAQ,QAAQ,KAAK,SAAS,QAAQ,gBAAgB,QAAQ,SAAS,QAAQ,gBAAgB,YAAY,sBAAsB,SAAS,QAAQ,IAAI,GAAG;AAChK,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,QAAQ,KAAK,OAAO,KAAK,CAAC,WAAW,OAAO,UAAU,QAAQ,CAAC;AACrE,UAAI,CAAC,OAAO;AACV,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,gBAAgB,OAAO,UAAU,SAAS,MAAM;AACrD,cAAM,MAAM,KAAK,uBAAuB,CAAC;AAAA,MAC3C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,OAAO,SAAS,SAAS,QAAQ;AAC/C,UAAM,QAAQ,KAAK,QAAQ,IAAI,KAAK;AACpC,QAAI,OAAO;AACT,YAAM,KAAK,CAAC,SAAS,SAAS,MAAM,CAAC;AAAA,IACvC,OAAO;AACL,WAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,SAAS,SAAS,MAAM,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AAAA,EACA,MAAM,yBAAyB;AAC7B,UAAM,UAAU,KAAK;AACrB,SAAK,UAA0B,oBAAI,IAAI;AACvC,eAAW,CAAC,OAAO,KAAK,KAAK,SAAS;AACpC,YAAM,WAAW,MAAM,OAAO,CAAC,CAAC,OAAO,MAAM,QAAQ,QAAQ,WAAW,KAAK;AAC7E,YAAM,YAAY,MAAM,OAAO,CAAC,CAAC,OAAO,MAAM,QAAQ,QAAQ,WAAW,KAAK;AAC9E,WAAK,cAAc,OAAO,OAAO,QAAQ;AACzC,WAAK,cAAc,QAAQ,OAAO,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,MAAM,cAAc,QAAQ,OAAO,YAAY;AAC7C,QAAI,CAAC,WAAW,QAAQ;AACtB;AAAA,IACF;AACA,UAAM,aAAa;AACnB,QAAI,WAAW,WAAW,GAAG;AAC3B,iBAAW,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,EAAE,CAAC,CAAC;AACrE;AAAA,IACF;AACA,QAAI;AACF,YAAM,UAAU,WAAW,IAAI,CAAC,CAAC,QAAQ,MAAM,QAAQ;AACvD,YAAM,UAAU,MAAM,MAAM,KAAK,SAAS,OAAO;AACjD,UAAI,WAAW,SAAS,SAAS;AAC/B,cAAM,CAAC,OAAO,MAAM,IAAI,YAAY,UAAU;AAC9C,aAAK,cAAc,QAAQ,OAAO,KAAK;AACvC,aAAK,cAAc,QAAQ,OAAO,MAAM;AACxC;AAAA,MACF;AACA,YAAM,WAAW,IAAI,IAAI,MAAM,MAAM,KAAK,UAAU,OAAO,CAAC;AAC5D,YAAM,eAAe,MAAM,MAAM,KAAK,cAAc,OAAO;AAC3D,YAAM,cAAc,WAAW,IAAI,CAAC,CAAC,QAAQ,MAAM,KAAK,eAAe,EAAE,GAAG,UAAU,UAAU,aAAa,CAAC,CAAC;AAC/G,YAAM,eAAe,eAAe;AAAA,QAClC;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,eAAe,MAAM,MAAM,KAAK,cAAc,OAAO;AAC3D,UAAI,aAAa,IAAI,SAAS,EAAE,SAAS,cAAc;AACrD,cAAM,CAAC,OAAO,MAAM,IAAI,YAAY,UAAU;AAC9C,aAAK,cAAc,QAAQ,OAAO,KAAK;AACvC,aAAK,cAAc,QAAQ,OAAO,MAAM;AACxC;AAAA,MACF;AACA,YAAM,OAAO,MAAM,KAAK,MAAM,OAAO;AACrC,YAAM,eAAe,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,QACzC,SAAS,EAAE,GAAG,cAAc,SAAS,EAAE,GAAG,aAAa,SAAS,gBAAgB,KAAK,EAAE;AAAA,QACvF,QAAQ,aAAa;AAAA,QACrB,SAAS,MAAM;AAAA,QACf,OAAO,MAAM;AAAA,QACb,MAAM,QAAQ,MAAM,IAAI;AAAA,MAC1B,CAAC;AACD,YAAM,SAAS,mBAAmB,EAAE,GAAG,cAAc,MAAM,MAAM,aAAa,KAAK,EAAE,CAAC;AACtF,uBAAiB,QAAQ,QAAQ;AAC/B,mBAAW,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,GAAG,MAAM,MAAM,MAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE,CAAC;AAAA,MACjF;AACA,YAAM,IAAI,MAAM,6GAA6G;AAAA,IAC/H,SAAS,OAAO;AACd,iBAAW,CAAC,EAAE,EAAE,MAAM,KAAK,YAAY;AACrC,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAAN,MAA2B;AAAA,EACzB;AAAA,EACA;AAAA,EACA,QAAQ;AAAA;AAAA,EAER,SAAyB,oBAAI,IAAI;AAAA,EACjC,YAAY,SAAS;AACnB,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,WAAW,CAAC,EAAE,QAAQ,MAAM,QAAQ,WAAW;AAAA,EACxE;AAAA,EACA,KAAK,SAAS;AACZ,YAAQ,uBAAuB,CAAC;AAChC,YAAQ,mBAAmB,KAAK,CAAC,aAAa;AAC5C,UAAI,SAAS,QAAQ,gBAAgB,QAAQ,SAAS,QAAQ,gBAAgB,YAAY,SAAS,QAAQ,gBAAgB,mBAAmB,sBAAsB,SAAS,QAAQ,IAAI,KAAK,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACrN,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,QAAQ,KAAK,QAAQ,KAAK,CAAC,WAAW,OAAO,UAAU,QAAQ,CAAC;AACtE,UAAI,CAAC,OAAO;AACV,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,SAAS,OAAO,UAAU,SAAS,MAAM;AAC9C,cAAM,MAAM,KAAK,SAAS,CAAC;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO,SAAS,SAAS,QAAQ;AACxC,QAAI,QAAQ,KAAK,OAAO,IAAI,KAAK;AACjC,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,CAAC;AAAA,IACnC;AACA,UAAM,UAAU,MAAM,KAAK,CAAC,SAAS;AACnC,YAAM,iBAAiB,cAAc;AAAA,QACnC,MAAM,KAAK,QAAQ,QAAQ;AAAA,QAC3B,SAAS,KAAK,QAAQ,QAAQ;AAAA,QAC9B,QAAQ,KAAK,QAAQ,QAAQ;AAAA,QAC7B,KAAK,KAAK,QAAQ,QAAQ;AAAA,MAC5B,CAAC;AACD,YAAM,iBAAiB,cAAc;AAAA,QACnC,MAAM,QAAQ,QAAQ;AAAA,QACtB,SAAS,QAAQ,QAAQ;AAAA,QACzB,QAAQ,QAAQ,QAAQ;AAAA,QACxB,KAAK,QAAQ,QAAQ;AAAA,MACvB,CAAC;AACD,aAAO,mBAAmB;AAAA,IAC5B,CAAC;AACD,QAAI,SAAS;AACX,cAAQ,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AAC3C,cAAQ,SAAS,KAAK,OAAO;AAC7B,cAAQ,QAAQ,KAAK,MAAM;AAAA,IAC7B,OAAO;AACL,YAAM,KAAK;AAAA,QACT;AAAA,QACA,SAAS,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAChC,UAAU,CAAC,OAAO;AAAA,QAClB,SAAS,CAAC,MAAM;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,WAAW;AACf,UAAM,WAAW,CAAC;AAClB,eAAW,CAAC,OAAO,KAAK,KAAK,KAAK,QAAQ;AACxC,iBAAW,EAAE,SAAS,SAAS,UAAU,QAAQ,KAAK,OAAO;AAC3D,iBAAS;AAAA,UACP,KAAK,SAAS,OAAO,SAAS,SAAS,UAAU,OAAO;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO,MAAM;AAClB,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAAA,EACA,MAAM,SAAS,OAAO,SAAS,SAAS,UAAU,SAAS;AACzD,QAAI;AACF,YAAM,iBAAiB;AAAA,QACrB,GAAG,QAAQ;AAAA,QACX,QAAQ,mBAAmB,OAAO;AAAA,MACpC;AACA,YAAM,WAAW,MAAM,QAAQ,KAAK;AAAA,QAClC,GAAG;AAAA,QACH,SAAS;AAAA,QACT,QAAQ,eAAe;AAAA,QACvB,SAAS,MAAM;AAAA,MACjB,CAAC;AACD,YAAM,sBAAsB,8BAA8B,UAAU,SAAS,MAAM;AACnF,iBAAW,WAAW,UAAU;AAC9B,gBAAQ,oBAAoB,MAAM,CAAC;AAAA,MACrC;AAAA,IACF,SAAS,OAAO;AACd,iBAAW,UAAU,SAAS;AAC5B,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,qDAAN,cAAiE,MAAM;AACvE;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,eAAe,QAAQ,SAAS,SAAS;AAC9C,SAAK,oBAAoB,QAAQ,SAAS,eAAe,CAAC,MAAM,EAAE,kBAAkB;AACpF,SAAK,qBAAqB,QAAQ,SAAS,eAAe;AAC1D,SAAK,iBAAiB,QAAQ,SAAS;AAAA,EACzC;AAAA,EACA,KAAK,SAAS;AACZ,YAAQ,iBAAiB,CAAC;AAC1B,YAAQ,aAAa,KAAK,OAAO,uBAAuB;AACtD,YAAM,cAAc,MAAM;AAAA,QACxB,mBAAmB,QAAQ,SAAS,KAAK;AAAA,QACzC;AAAA,MACF;AACA,YAAM,aAAa,mBAAmB,QAAQ,cAAc,KAAK;AACjE,YAAM,cAAc,mBAAmB,QAAQ,eAAe,KAAK;AACnE,YAAM,UAAU,mBAAmB,QAAQ,WAAW,KAAK;AAC3D,UAAI,eAAe,GAAG;AACpB,eAAO,mBAAmB,KAAK;AAAA,MACjC;AACA,UAAI,cAAc,mBAAmB;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe;AACnB,YAAM,OAAO,OAAO,iBAAiB;AACnC,YAAI,eAAe;AACnB,eAAO,MAAM;AACX,gBAAM,4BAA4B,EAAE,GAAG,oBAAoB,YAAY;AACvE,cAAI,cAAc;AAChB,gBAAI,gBAAgB,aAAa;AAC/B,oBAAM,aAAa;AAAA,YACrB;AACA,kBAAM,iBAAiB;AAAA,cACrB,GAAG;AAAA,cACH;AAAA,cACA,OAAO,aAAa;AAAA,cACpB;AAAA,YACF;AACA,kBAAM,kBAAkB,MAAM;AAAA,cAC5B;AAAA,cACA;AAAA,YACF;AACA,gBAAI,CAAC,iBAAiB;AACpB,oBAAM,aAAa;AAAA,YACrB;AACA,uBAAW,UAAU,cAAc;AACnC,kBAAM,eAAe,MAAM,MAAM,YAAY,cAAc;AAC3D,kBAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,YAAY,CAAC;AAChE;AAAA,UACF;AACA,cAAI;AACF,2BAAe;AACf,mBAAO,MAAM,mBAAmB,KAAK,yBAAyB;AAAA,UAChE,SAAS,OAAO;AACd,2BAAe,EAAE,MAAM;AACvB,gBAAI,0BAA0B,QAAQ,YAAY,MAAM;AACtD,oBAAM;AAAA,YACR;AAAA,UACF,UAAE;AACA,uBAAW,CAAC,YAAY;AACxB,uBAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,MAAM,KAAK;AAC1B,UAAI,CAAC,sBAAsB,MAAM,GAAG;AAClC,eAAO;AAAA,MACT;AACA,aAAO,mBAAmB;AACxB,YAAI,UAAU;AACd,YAAI;AACF,iBAAO,MAAM;AACX,gBAAI;AACF,oBAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,oBAAM,OAAO,aAAa,KAAK,KAAK;AACpC,4BAAc,MAAM,MAAM;AAC1B,+BAAiB,MAAM,SAAS;AAChC,kBAAI,KAAK,MAAM;AACb,uBAAO,KAAK;AAAA,cACd;AACA,oBAAM,KAAK;AAAA,YACb,SAAS,OAAO;AACd,oBAAM,OAAO,aAAa,KAAK;AAC/B,4BAAc,MAAM,MAAM;AAC1B,+BAAiB,MAAM,SAAS;AAChC,oBAAM,qBAAqB,MAAM,KAAK,EAAE,MAAM,CAAC;AAC/C,kBAAI,CAAC,sBAAsB,kBAAkB,GAAG;AAC9C,sBAAM,IAAI;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AACA,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,UAAE;AACA,gBAAM,QAAQ,SAAS;AAAA,QACzB;AAAA,MACF,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AACF;AAEA,IAAM,iCAAN,MAAqC;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,aAAa,QAAQ,cAAc;AACxC,SAAK,cAAc,QAAQ,eAAe;AAC1C,SAAK,UAAU,QAAQ,WAAW;AAAA,EACpC;AAAA,EACA,QAAQ;AAAA,EACR,KAAK,SAAS;AACZ,YAAQ,uBAAuB,CAAC;AAChC,YAAQ,mBAAmB,KAAK,OAAO,aAAa;AAClD,YAAM,WAAW,MAAM,MAAM,KAAK,SAAS,QAAQ;AACnD,UAAI,UAAU;AACZ,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,aAAa,MAAM,MAAM,KAAK,YAAY,QAAQ;AACxD,YAAM,cAAc,MAAM,MAAM,KAAK,aAAa,QAAQ;AAC1D,aAAO,SAAS,KAAK;AAAA,QACnB,GAAG;AAAA,QACH,SAAS;AAAA,UACP,GAAG,SAAS;AAAA,UACZ,SAAS;AAAA,YACP,GAAG,SAAS,QAAQ;AAAA,YACpB,CAAC,UAAU,GAAG;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;", "names": []}