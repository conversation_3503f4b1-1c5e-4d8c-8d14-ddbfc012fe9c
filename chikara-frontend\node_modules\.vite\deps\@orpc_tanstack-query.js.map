{"version": 3, "sources": ["../../../../node_modules/.bun/@orpc+tanstack-query@1.7.4+774786d88e8a23fe/node_modules/@orpc/tanstack-query/dist/index.mjs"], "sourcesContent": ["import { stringifyJSON, isAsyncIteratorObject, toArray } from '@orpc/shared';\nimport { skipToken, experimental_streamedQuery } from '@tanstack/query-core';\n\nfunction generateOperationKey(path, state = {}) {\n  return [path, {\n    ...state.input !== void 0 ? { input: state.input } : {},\n    ...state.type !== void 0 ? { type: state.type } : {},\n    ...state.fnOptions !== void 0 ? { fnOptions: state.fnOptions } : {}\n  }];\n}\n\nfunction createGeneralUtils(path) {\n  return {\n    key(options) {\n      return generateOperationKey(path, options);\n    }\n  };\n}\n\nfunction experimental_liveQuery(queryFn) {\n  return async (context) => {\n    const stream = await queryFn(context);\n    let last;\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break;\n      }\n      last = { chunk };\n      context.client.setQueryData(context.queryKey, chunk);\n    }\n    if (!last) {\n      throw new Error(\n        `Live query for ${stringifyJSON(context.queryKey)} did not yield any data. Ensure the query function returns an AsyncIterable with at least one chunk.`\n      );\n    }\n    return last.chunk;\n  };\n}\n\nconst OPERATION_CONTEXT_SYMBOL = Symbol(\"ORPC_OPERATION_CONTEXT\");\n\nfunction createProcedureUtils(client, options) {\n  const utils = {\n    call: client,\n    queryKey(...[optionsIn = {}]) {\n      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: \"query\", input: optionsIn.input });\n      return queryKey;\n    },\n    queryOptions(...[optionsIn = {}]) {\n      const queryKey = utils.queryKey(optionsIn);\n      return {\n        queryFn: ({ signal }) => {\n          if (optionsIn.input === skipToken) {\n            throw new Error(\"queryFn should not be called with skipToken used as input\");\n          }\n          return client(optionsIn.input, {\n            signal,\n            context: {\n              [OPERATION_CONTEXT_SYMBOL]: {\n                key: queryKey,\n                type: \"query\"\n              },\n              ...optionsIn.context\n            }\n          });\n        },\n        enabled: optionsIn.input !== skipToken,\n        ...optionsIn,\n        queryKey\n      };\n    },\n    experimental_streamedKey(...[optionsIn = {}]) {\n      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: \"streamed\", input: optionsIn.input, fnOptions: optionsIn.queryFnOptions });\n      return queryKey;\n    },\n    experimental_streamedOptions(...[optionsIn = {}]) {\n      const queryKey = utils.experimental_streamedKey(optionsIn);\n      return {\n        enabled: optionsIn.input !== skipToken,\n        queryFn: experimental_streamedQuery({\n          queryFn: async ({ signal }) => {\n            if (optionsIn.input === skipToken) {\n              throw new Error(\"queryFn should not be called with skipToken used as input\");\n            }\n            const output = await client(optionsIn.input, {\n              signal,\n              context: {\n                [OPERATION_CONTEXT_SYMBOL]: {\n                  key: queryKey,\n                  type: \"streamed\"\n                },\n                ...optionsIn.context\n              }\n            });\n            if (!isAsyncIteratorObject(output)) {\n              throw new Error(\"streamedQuery requires an event iterator output\");\n            }\n            return output;\n          },\n          ...optionsIn.queryFnOptions\n        }),\n        ...optionsIn,\n        queryKey\n      };\n    },\n    experimental_liveKey(...[optionsIn = {}]) {\n      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: \"live\", input: optionsIn.input });\n      return queryKey;\n    },\n    experimental_liveOptions(...[optionsIn = {}]) {\n      const queryKey = utils.experimental_liveKey(optionsIn);\n      return {\n        enabled: optionsIn.input !== skipToken,\n        queryFn: experimental_liveQuery(async ({ signal }) => {\n          if (optionsIn.input === skipToken) {\n            throw new Error(\"queryFn should not be called with skipToken used as input\");\n          }\n          const output = await client(optionsIn.input, {\n            signal,\n            context: {\n              [OPERATION_CONTEXT_SYMBOL]: {\n                key: queryKey,\n                type: \"live\"\n              },\n              ...optionsIn.context\n            }\n          });\n          if (!isAsyncIteratorObject(output)) {\n            throw new Error(\"liveQuery requires an event iterator output\");\n          }\n          return output;\n        }),\n        ...optionsIn,\n        queryKey\n      };\n    },\n    infiniteKey(optionsIn) {\n      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, {\n        type: \"infinite\",\n        input: optionsIn.input === skipToken ? skipToken : optionsIn.input(optionsIn.initialPageParam)\n      });\n      return queryKey;\n    },\n    infiniteOptions(optionsIn) {\n      const queryKey = utils.infiniteKey(optionsIn);\n      return {\n        queryFn: ({ pageParam, signal }) => {\n          if (optionsIn.input === skipToken) {\n            throw new Error(\"queryFn should not be called with skipToken used as input\");\n          }\n          return client(optionsIn.input(pageParam), {\n            signal,\n            context: {\n              [OPERATION_CONTEXT_SYMBOL]: {\n                key: queryKey,\n                type: \"infinite\"\n              },\n              ...optionsIn.context\n            }\n          });\n        },\n        enabled: optionsIn.input !== skipToken,\n        ...optionsIn,\n        queryKey\n      };\n    },\n    mutationKey(...[optionsIn = {}]) {\n      const mutationKey = optionsIn.mutationKey ?? generateOperationKey(options.path, { type: \"mutation\" });\n      return mutationKey;\n    },\n    mutationOptions(...[optionsIn = {}]) {\n      const mutationKey = utils.mutationKey(optionsIn);\n      return {\n        mutationFn: (input) => client(input, {\n          context: {\n            [OPERATION_CONTEXT_SYMBOL]: {\n              key: mutationKey,\n              type: \"mutation\"\n            },\n            ...optionsIn.context\n          }\n        }),\n        ...optionsIn,\n        mutationKey\n      };\n    }\n  };\n  return utils;\n}\n\nfunction createRouterUtils(client, options = {}) {\n  const path = toArray(options.path);\n  const generalUtils = createGeneralUtils(path);\n  const procedureUtils = createProcedureUtils(client, { path });\n  const recursive = new Proxy({\n    ...generalUtils,\n    ...procedureUtils\n  }, {\n    get(target, prop) {\n      const value = Reflect.get(target, prop);\n      if (typeof prop !== \"string\") {\n        return value;\n      }\n      const nextUtils = createRouterUtils(client[prop], { ...options, path: [...path, prop] });\n      if (typeof value !== \"function\") {\n        return nextUtils;\n      }\n      return new Proxy(value, {\n        get(_, prop2) {\n          return Reflect.get(nextUtils, prop2);\n        }\n      });\n    }\n  });\n  return recursive;\n}\n\nexport { OPERATION_CONTEXT_SYMBOL, OPERATION_CONTEXT_SYMBOL as TANSTACK_QUERY_OPERATION_CONTEXT_SYMBOL, createGeneralUtils, createProcedureUtils, createRouterUtils, createRouterUtils as createTanstackQueryUtils, generateOperationKey };\n"], "mappings": ";;;;;;;;;;;;AAGA,SAAS,qBAAqB,MAAM,QAAQ,CAAC,GAAG;AAC9C,SAAO,CAAC,MAAM;AAAA,IACZ,GAAG,MAAM,UAAU,SAAS,EAAE,OAAO,MAAM,MAAM,IAAI,CAAC;AAAA,IACtD,GAAG,MAAM,SAAS,SAAS,EAAE,MAAM,MAAM,KAAK,IAAI,CAAC;AAAA,IACnD,GAAG,MAAM,cAAc,SAAS,EAAE,WAAW,MAAM,UAAU,IAAI,CAAC;AAAA,EACpE,CAAC;AACH;AAEA,SAAS,mBAAmB,MAAM;AAChC,SAAO;AAAA,IACL,IAAI,SAAS;AACX,aAAO,qBAAqB,MAAM,OAAO;AAAA,IAC3C;AAAA,EACF;AACF;AAEA,SAAS,uBAAuB,SAAS;AACvC,SAAO,OAAO,YAAY;AACxB,UAAM,SAAS,MAAM,QAAQ,OAAO;AACpC,QAAI;AACJ,qBAAiB,SAAS,QAAQ;AAChC,UAAI,QAAQ,OAAO,SAAS;AAC1B;AAAA,MACF;AACA,aAAO,EAAE,MAAM;AACf,cAAQ,OAAO,aAAa,QAAQ,UAAU,KAAK;AAAA,IACrD;AACA,QAAI,CAAC,MAAM;AACT,YAAM,IAAI;AAAA,QACR,kBAAkB,cAAc,QAAQ,QAAQ,CAAC;AAAA,MACnD;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAM,2BAA2B,OAAO,wBAAwB;AAEhE,SAAS,qBAAqB,QAAQ,SAAS;AAC7C,QAAM,QAAQ;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC,YAAY,CAAC,CAAC,GAAG;AAC5B,YAAM,WAAW,UAAU,YAAY,qBAAqB,QAAQ,MAAM,EAAE,MAAM,SAAS,OAAO,UAAU,MAAM,CAAC;AACnH,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG;AAChC,YAAM,WAAW,MAAM,SAAS,SAAS;AACzC,aAAO;AAAA,QACL,SAAS,CAAC,EAAE,OAAO,MAAM;AACvB,cAAI,UAAU,UAAU,WAAW;AACjC,kBAAM,IAAI,MAAM,2DAA2D;AAAA,UAC7E;AACA,iBAAO,OAAO,UAAU,OAAO;AAAA,YAC7B;AAAA,YACA,SAAS;AAAA,cACP,CAAC,wBAAwB,GAAG;AAAA,gBAC1B,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA,GAAG,UAAU;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,SAAS,UAAU,UAAU;AAAA,QAC7B,GAAG;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,YAAY,CAAC,CAAC,GAAG;AAC5C,YAAM,WAAW,UAAU,YAAY,qBAAqB,QAAQ,MAAM,EAAE,MAAM,YAAY,OAAO,UAAU,OAAO,WAAW,UAAU,eAAe,CAAC;AAC3J,aAAO;AAAA,IACT;AAAA,IACA,gCAAgC,CAAC,YAAY,CAAC,CAAC,GAAG;AAChD,YAAM,WAAW,MAAM,yBAAyB,SAAS;AACzD,aAAO;AAAA,QACL,SAAS,UAAU,UAAU;AAAA,QAC7B,SAAS,cAA2B;AAAA,UAClC,SAAS,OAAO,EAAE,OAAO,MAAM;AAC7B,gBAAI,UAAU,UAAU,WAAW;AACjC,oBAAM,IAAI,MAAM,2DAA2D;AAAA,YAC7E;AACA,kBAAM,SAAS,MAAM,OAAO,UAAU,OAAO;AAAA,cAC3C;AAAA,cACA,SAAS;AAAA,gBACP,CAAC,wBAAwB,GAAG;AAAA,kBAC1B,KAAK;AAAA,kBACL,MAAM;AAAA,gBACR;AAAA,gBACA,GAAG,UAAU;AAAA,cACf;AAAA,YACF,CAAC;AACD,gBAAI,CAAC,sBAAsB,MAAM,GAAG;AAClC,oBAAM,IAAI,MAAM,iDAAiD;AAAA,YACnE;AACA,mBAAO;AAAA,UACT;AAAA,UACA,GAAG,UAAU;AAAA,QACf,CAAC;AAAA,QACD,GAAG;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,IACA,wBAAwB,CAAC,YAAY,CAAC,CAAC,GAAG;AACxC,YAAM,WAAW,UAAU,YAAY,qBAAqB,QAAQ,MAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,MAAM,CAAC;AAClH,aAAO;AAAA,IACT;AAAA,IACA,4BAA4B,CAAC,YAAY,CAAC,CAAC,GAAG;AAC5C,YAAM,WAAW,MAAM,qBAAqB,SAAS;AACrD,aAAO;AAAA,QACL,SAAS,UAAU,UAAU;AAAA,QAC7B,SAAS,uBAAuB,OAAO,EAAE,OAAO,MAAM;AACpD,cAAI,UAAU,UAAU,WAAW;AACjC,kBAAM,IAAI,MAAM,2DAA2D;AAAA,UAC7E;AACA,gBAAM,SAAS,MAAM,OAAO,UAAU,OAAO;AAAA,YAC3C;AAAA,YACA,SAAS;AAAA,cACP,CAAC,wBAAwB,GAAG;AAAA,gBAC1B,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA,GAAG,UAAU;AAAA,YACf;AAAA,UACF,CAAC;AACD,cAAI,CAAC,sBAAsB,MAAM,GAAG;AAClC,kBAAM,IAAI,MAAM,6CAA6C;AAAA,UAC/D;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,QACD,GAAG;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY,WAAW;AACrB,YAAM,WAAW,UAAU,YAAY,qBAAqB,QAAQ,MAAM;AAAA,QACxE,MAAM;AAAA,QACN,OAAO,UAAU,UAAU,YAAY,YAAY,UAAU,MAAM,UAAU,gBAAgB;AAAA,MAC/F,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,WAAW;AACzB,YAAM,WAAW,MAAM,YAAY,SAAS;AAC5C,aAAO;AAAA,QACL,SAAS,CAAC,EAAE,WAAW,OAAO,MAAM;AAClC,cAAI,UAAU,UAAU,WAAW;AACjC,kBAAM,IAAI,MAAM,2DAA2D;AAAA,UAC7E;AACA,iBAAO,OAAO,UAAU,MAAM,SAAS,GAAG;AAAA,YACxC;AAAA,YACA,SAAS;AAAA,cACP,CAAC,wBAAwB,GAAG;AAAA,gBAC1B,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA,GAAG,UAAU;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,SAAS,UAAU,UAAU;AAAA,QAC7B,GAAG;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe,CAAC,YAAY,CAAC,CAAC,GAAG;AAC/B,YAAM,cAAc,UAAU,eAAe,qBAAqB,QAAQ,MAAM,EAAE,MAAM,WAAW,CAAC;AACpG,aAAO;AAAA,IACT;AAAA,IACA,mBAAmB,CAAC,YAAY,CAAC,CAAC,GAAG;AACnC,YAAM,cAAc,MAAM,YAAY,SAAS;AAC/C,aAAO;AAAA,QACL,YAAY,CAAC,UAAU,OAAO,OAAO;AAAA,UACnC,SAAS;AAAA,YACP,CAAC,wBAAwB,GAAG;AAAA,cAC1B,KAAK;AAAA,cACL,MAAM;AAAA,YACR;AAAA,YACA,GAAG,UAAU;AAAA,UACf;AAAA,QACF,CAAC;AAAA,QACD,GAAG;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC/C,QAAM,OAAO,QAAQ,QAAQ,IAAI;AACjC,QAAM,eAAe,mBAAmB,IAAI;AAC5C,QAAM,iBAAiB,qBAAqB,QAAQ,EAAE,KAAK,CAAC;AAC5D,QAAM,YAAY,IAAI,MAAM;AAAA,IAC1B,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAAA,IACD,IAAI,QAAQ,MAAM;AAChB,YAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI;AACtC,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,YAAM,YAAY,kBAAkB,OAAO,IAAI,GAAG,EAAE,GAAG,SAAS,MAAM,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AACvF,UAAI,OAAO,UAAU,YAAY;AAC/B,eAAO;AAAA,MACT;AACA,aAAO,IAAI,MAAM,OAAO;AAAA,QACtB,IAAI,GAAG,OAAO;AACZ,iBAAO,QAAQ,IAAI,WAAW,KAAK;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": []}