{"version": 3, "sources": ["../../../../node_modules/.bun/react-countdown@2.3.6+dfc90389513bb65c/node_modules/react-countdown/dist/index.es.js"], "sourcesContent": ["import { cloneElement, Component, createElement } from 'react';\nimport { number, element, func, oneOfType, instanceOf, string, bool } from 'prop-types';\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction zeroPad(value) {\n  var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n  var strValue = String(value);\n  if (length === 0) return strValue;\n  var match = strValue.match(/(.*?)([0-9]+)(.*)/);\n  var prefix = match ? match[1] : '';\n  var suffix = match ? match[3] : '';\n  var strNo = match ? match[2] : strValue;\n  var paddedNo = strNo.length >= length ? strNo : (_toConsumableArray(Array(length)).map(function () {\n    return '0';\n  }).join('') + strNo).slice(length * -1);\n  return \"\".concat(prefix).concat(paddedNo).concat(suffix);\n}\nvar timeDeltaFormatOptionsDefaults = {\n  daysInHours: false,\n  zeroPadTime: 2\n};\nfunction calcTimeDelta(date) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _options$now = options.now,\n      now = _options$now === void 0 ? Date.now : _options$now,\n      _options$precision = options.precision,\n      precision = _options$precision === void 0 ? 0 : _options$precision,\n      controlled = options.controlled,\n      _options$offsetTime = options.offsetTime,\n      offsetTime = _options$offsetTime === void 0 ? 0 : _options$offsetTime,\n      overtime = options.overtime;\n  var startTimestamp;\n\n  if (typeof date === 'string') {\n    startTimestamp = new Date(date).getTime();\n  } else if (date instanceof Date) {\n    startTimestamp = date.getTime();\n  } else {\n    startTimestamp = date;\n  }\n\n  if (!controlled) {\n    startTimestamp += offsetTime;\n  }\n\n  var timeLeft = controlled ? startTimestamp : startTimestamp - now();\n  var clampedPrecision = Math.min(20, Math.max(0, precision));\n  var total = Math.round(parseFloat(((overtime ? timeLeft : Math.max(0, timeLeft)) / 1000).toFixed(clampedPrecision)) * 1000);\n  var seconds = Math.abs(total) / 1000;\n  return {\n    total: total,\n    days: Math.floor(seconds / (3600 * 24)),\n    hours: Math.floor(seconds / 3600 % 24),\n    minutes: Math.floor(seconds / 60 % 60),\n    seconds: Math.floor(seconds % 60),\n    milliseconds: Number((seconds % 1 * 1000).toFixed()),\n    completed: total <= 0\n  };\n}\nfunction formatTimeDelta(timeDelta, options) {\n  var days = timeDelta.days,\n      hours = timeDelta.hours,\n      minutes = timeDelta.minutes,\n      seconds = timeDelta.seconds;\n\n  var _Object$assign = Object.assign(Object.assign({}, timeDeltaFormatOptionsDefaults), options),\n      daysInHours = _Object$assign.daysInHours,\n      zeroPadTime = _Object$assign.zeroPadTime,\n      _Object$assign$zeroPa = _Object$assign.zeroPadDays,\n      zeroPadDays = _Object$assign$zeroPa === void 0 ? zeroPadTime : _Object$assign$zeroPa;\n\n  var zeroPadTimeLength = Math.min(2, zeroPadTime);\n  var formattedHours = daysInHours ? zeroPad(hours + days * 24, zeroPadTime) : zeroPad(hours, zeroPadTimeLength);\n  return {\n    days: daysInHours ? '' : zeroPad(days, zeroPadDays),\n    hours: formattedHours,\n    minutes: zeroPad(minutes, zeroPadTimeLength),\n    seconds: zeroPad(seconds, zeroPadTimeLength)\n  };\n}\n\nvar Countdown = function (_React$Component) {\n  _inherits(Countdown, _React$Component);\n\n  var _super = _createSuper(Countdown);\n\n  function Countdown() {\n    var _this;\n\n    _classCallCheck(this, Countdown);\n\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      count: _this.props.count || 3\n    };\n\n    _this.startCountdown = function () {\n      _this.interval = window.setInterval(function () {\n        var count = _this.state.count - 1;\n\n        if (count === 0) {\n          _this.stopCountdown();\n\n          _this.props.onComplete && _this.props.onComplete();\n        } else {\n          _this.setState(function (prevState) {\n            return {\n              count: prevState.count - 1\n            };\n          });\n        }\n      }, 1000);\n    };\n\n    _this.stopCountdown = function () {\n      clearInterval(_this.interval);\n    };\n\n    _this.addTime = function (seconds) {\n      _this.stopCountdown();\n\n      _this.setState(function (prevState) {\n        return {\n          count: prevState.count + seconds\n        };\n      }, _this.startCountdown);\n    };\n\n    return _this;\n  }\n\n  _createClass(Countdown, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCountdown();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      clearInterval(this.interval);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return this.props.children ? cloneElement(this.props.children, {\n        count: this.state.count\n      }) : null;\n    }\n  }]);\n\n  return Countdown;\n}(Component);\nCountdown.propTypes = {\n  count: number,\n  children: element,\n  onComplete: func\n};\n\nvar Countdown$1 = function (_React$Component) {\n  _inherits(Countdown$1, _React$Component);\n\n  var _super = _createSuper(Countdown$1);\n\n  function Countdown$1(props) {\n    var _this;\n\n    _classCallCheck(this, Countdown$1);\n\n    _this = _super.call(this, props);\n    _this.mounted = false;\n    _this.initialTimestamp = _this.calcOffsetStartTimestamp();\n    _this.offsetStartTimestamp = _this.props.autoStart ? 0 : _this.initialTimestamp;\n    _this.offsetTime = 0;\n    _this.legacyMode = false;\n    _this.legacyCountdownRef = null;\n\n    _this.tick = function () {\n      var timeDelta = _this.calcTimeDelta();\n\n      var callback = timeDelta.completed && !_this.props.overtime ? undefined : _this.props.onTick;\n\n      _this.setTimeDeltaState(timeDelta, undefined, callback);\n    };\n\n    _this.setLegacyCountdownRef = function (ref) {\n      _this.legacyCountdownRef = ref;\n    };\n\n    _this.start = function () {\n      if (_this.isStarted()) return;\n      var prevOffsetStartTimestamp = _this.offsetStartTimestamp;\n      _this.offsetStartTimestamp = 0;\n      _this.offsetTime += prevOffsetStartTimestamp ? _this.calcOffsetStartTimestamp() - prevOffsetStartTimestamp : 0;\n\n      var timeDelta = _this.calcTimeDelta();\n\n      _this.setTimeDeltaState(timeDelta, \"STARTED\", _this.props.onStart);\n\n      if (!_this.props.controlled && (!timeDelta.completed || _this.props.overtime)) {\n        _this.clearTimer();\n\n        _this.interval = window.setInterval(_this.tick, _this.props.intervalDelay);\n      }\n    };\n\n    _this.pause = function () {\n      if (_this.isPaused()) return;\n\n      _this.clearTimer();\n\n      _this.offsetStartTimestamp = _this.calcOffsetStartTimestamp();\n\n      _this.setTimeDeltaState(_this.state.timeDelta, \"PAUSED\", _this.props.onPause);\n    };\n\n    _this.stop = function () {\n      if (_this.isStopped()) return;\n\n      _this.clearTimer();\n\n      _this.offsetStartTimestamp = _this.calcOffsetStartTimestamp();\n      _this.offsetTime = _this.offsetStartTimestamp - _this.initialTimestamp;\n\n      _this.setTimeDeltaState(_this.calcTimeDelta(), \"STOPPED\", _this.props.onStop);\n    };\n\n    _this.isStarted = function () {\n      return _this.isStatus(\"STARTED\");\n    };\n\n    _this.isPaused = function () {\n      return _this.isStatus(\"PAUSED\");\n    };\n\n    _this.isStopped = function () {\n      return _this.isStatus(\"STOPPED\");\n    };\n\n    _this.isCompleted = function () {\n      return _this.isStatus(\"COMPLETED\");\n    };\n\n    if (props.date) {\n      var timeDelta = _this.calcTimeDelta();\n\n      _this.state = {\n        timeDelta: timeDelta,\n        status: timeDelta.completed ? \"COMPLETED\" : \"STOPPED\"\n      };\n    } else {\n      _this.legacyMode = true;\n    }\n\n    return _this;\n  }\n\n  _createClass(Countdown$1, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.legacyMode) {\n        return;\n      }\n\n      this.mounted = true;\n      if (this.props.onMount) this.props.onMount(this.calcTimeDelta());\n      if (this.props.autoStart) this.start();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.legacyMode) {\n        return;\n      }\n\n      if (this.props.date !== prevProps.date) {\n        this.initialTimestamp = this.calcOffsetStartTimestamp();\n        this.offsetStartTimestamp = this.initialTimestamp;\n        this.offsetTime = 0;\n        this.setTimeDeltaState(this.calcTimeDelta());\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.legacyMode) {\n        return;\n      }\n\n      this.mounted = false;\n      this.clearTimer();\n    }\n  }, {\n    key: \"calcTimeDelta\",\n    value: function calcTimeDelta$1() {\n      var _this$props = this.props,\n          date = _this$props.date,\n          now = _this$props.now,\n          precision = _this$props.precision,\n          controlled = _this$props.controlled,\n          overtime = _this$props.overtime;\n      return calcTimeDelta(date, {\n        now: now,\n        precision: precision,\n        controlled: controlled,\n        offsetTime: this.offsetTime,\n        overtime: overtime\n      });\n    }\n  }, {\n    key: \"calcOffsetStartTimestamp\",\n    value: function calcOffsetStartTimestamp() {\n      return Date.now();\n    }\n  }, {\n    key: \"addTime\",\n    value: function addTime(seconds) {\n      this.legacyCountdownRef.addTime(seconds);\n    }\n  }, {\n    key: \"clearTimer\",\n    value: function clearTimer() {\n      window.clearInterval(this.interval);\n    }\n  }, {\n    key: \"isStatus\",\n    value: function isStatus(status) {\n      return this.state.status === status;\n    }\n  }, {\n    key: \"setTimeDeltaState\",\n    value: function setTimeDeltaState(timeDelta, status, callback) {\n      var _this2 = this;\n\n      if (!this.mounted) return;\n      var completing = timeDelta.completed && !this.state.timeDelta.completed;\n      var completedOnStart = timeDelta.completed && status === \"STARTED\";\n\n      if (completing && !this.props.overtime) {\n        this.clearTimer();\n      }\n\n      var onDone = function onDone() {\n        if (callback) callback(_this2.state.timeDelta);\n\n        if (_this2.props.onComplete && (completing || completedOnStart)) {\n          _this2.props.onComplete(timeDelta, completedOnStart);\n        }\n      };\n\n      return this.setState(function (prevState) {\n        var newStatus = status || prevState.status;\n\n        if (timeDelta.completed && !_this2.props.overtime) {\n          newStatus = \"COMPLETED\";\n        } else if (!status && newStatus === \"COMPLETED\") {\n          newStatus = \"STOPPED\";\n        }\n\n        return {\n          timeDelta: timeDelta,\n          status: newStatus\n        };\n      }, onDone);\n    }\n  }, {\n    key: \"getApi\",\n    value: function getApi() {\n      return this.api = this.api || {\n        start: this.start,\n        pause: this.pause,\n        stop: this.stop,\n        isStarted: this.isStarted,\n        isPaused: this.isPaused,\n        isStopped: this.isStopped,\n        isCompleted: this.isCompleted\n      };\n    }\n  }, {\n    key: \"getRenderProps\",\n    value: function getRenderProps() {\n      var _this$props2 = this.props,\n          daysInHours = _this$props2.daysInHours,\n          zeroPadTime = _this$props2.zeroPadTime,\n          zeroPadDays = _this$props2.zeroPadDays;\n      var timeDelta = this.state.timeDelta;\n      return Object.assign(Object.assign({}, timeDelta), {\n        api: this.getApi(),\n        props: this.props,\n        formatted: formatTimeDelta(timeDelta, {\n          daysInHours: daysInHours,\n          zeroPadTime: zeroPadTime,\n          zeroPadDays: zeroPadDays\n        })\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (this.legacyMode) {\n        var _this$props3 = this.props,\n            count = _this$props3.count,\n            _children = _this$props3.children,\n            onComplete = _this$props3.onComplete;\n        return createElement(Countdown, {\n          ref: this.setLegacyCountdownRef,\n          count: count,\n          onComplete: onComplete\n        }, _children);\n      }\n\n      var _this$props4 = this.props,\n          className = _this$props4.className,\n          overtime = _this$props4.overtime,\n          children = _this$props4.children,\n          renderer = _this$props4.renderer;\n      var renderProps = this.getRenderProps();\n\n      if (renderer) {\n        return renderer(renderProps);\n      }\n\n      if (children && this.state.timeDelta.completed && !overtime) {\n        return cloneElement(children, {\n          countdown: renderProps\n        });\n      }\n\n      var _renderProps$formatte = renderProps.formatted,\n          days = _renderProps$formatte.days,\n          hours = _renderProps$formatte.hours,\n          minutes = _renderProps$formatte.minutes,\n          seconds = _renderProps$formatte.seconds;\n      return createElement(\"span\", {\n        className: className\n      }, renderProps.total < 0 ? '-' : '', days, days ? ':' : '', hours, \":\", minutes, \":\", seconds);\n    }\n  }]);\n\n  return Countdown$1;\n}(Component);\nCountdown$1.defaultProps = Object.assign(Object.assign({}, timeDeltaFormatOptionsDefaults), {\n  controlled: false,\n  intervalDelay: 1000,\n  precision: 0,\n  autoStart: true\n});\nCountdown$1.propTypes = {\n  date: oneOfType([instanceOf(Date), string, number]),\n  daysInHours: bool,\n  zeroPadTime: number,\n  zeroPadDays: number,\n  controlled: bool,\n  intervalDelay: number,\n  precision: number,\n  autoStart: bool,\n  overtime: bool,\n  className: string,\n  children: element,\n  renderer: func,\n  now: func,\n  onMount: func,\n  onStart: func,\n  onPause: func,\n  onStop: func,\n  onTick: func,\n  onComplete: func\n};\n\nexport default Countdown$1;\nexport { calcTimeDelta, formatTimeDelta, zeroPad };\n"], "mappings": ";;;;;;;;;;;;AAAA,mBAAuD;AACvD,wBAA2E;AAE3E,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,WAAY,iBAAgB,UAAU,UAAU;AACtD;AAEA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASA,iBAAgBC,IAAG;AAC5F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AACxE,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AAEA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AACjE,MAAI,QAAQ,UAAU,KAAM,QAAO;AACnC,MAAI,OAAO,UAAU,WAAY,QAAO;AAExC,MAAI;AACF,SAAK,UAAU,SAAS,KAAK,QAAQ,UAAU,MAAM,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AACxE,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAO;AACT;AAEA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuB,IAAI;AACpC;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAE1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAC/B;AAEJ,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AAEtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AAEA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,IAAI,EAAG,QAAO,MAAM,KAAK,IAAI;AAC9F;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,QAAQ,OAAO;AACtB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAI,WAAW,OAAO,KAAK;AAC3B,MAAI,WAAW,EAAG,QAAO;AACzB,MAAI,QAAQ,SAAS,MAAM,mBAAmB;AAC9C,MAAI,SAAS,QAAQ,MAAM,CAAC,IAAI;AAChC,MAAI,SAAS,QAAQ,MAAM,CAAC,IAAI;AAChC,MAAI,QAAQ,QAAQ,MAAM,CAAC,IAAI;AAC/B,MAAI,WAAW,MAAM,UAAU,SAAS,SAAS,mBAAmB,MAAM,MAAM,CAAC,EAAE,IAAI,WAAY;AACjG,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE,IAAI,OAAO,MAAM,SAAS,EAAE;AACtC,SAAO,GAAG,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AACzD;AACA,IAAI,iCAAiC;AAAA,EACnC,aAAa;AAAA,EACb,aAAa;AACf;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI,eAAe,QAAQ,KACvB,MAAM,iBAAiB,SAAS,KAAK,MAAM,cAC3C,qBAAqB,QAAQ,WAC7B,YAAY,uBAAuB,SAAS,IAAI,oBAChD,aAAa,QAAQ,YACrB,sBAAsB,QAAQ,YAC9B,aAAa,wBAAwB,SAAS,IAAI,qBAClD,WAAW,QAAQ;AACvB,MAAI;AAEJ,MAAI,OAAO,SAAS,UAAU;AAC5B,qBAAiB,IAAI,KAAK,IAAI,EAAE,QAAQ;AAAA,EAC1C,WAAW,gBAAgB,MAAM;AAC/B,qBAAiB,KAAK,QAAQ;AAAA,EAChC,OAAO;AACL,qBAAiB;AAAA,EACnB;AAEA,MAAI,CAAC,YAAY;AACf,sBAAkB;AAAA,EACpB;AAEA,MAAI,WAAW,aAAa,iBAAiB,iBAAiB,IAAI;AAClE,MAAI,mBAAmB,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;AAC1D,MAAI,QAAQ,KAAK,MAAM,aAAa,WAAW,WAAW,KAAK,IAAI,GAAG,QAAQ,KAAK,KAAM,QAAQ,gBAAgB,CAAC,IAAI,GAAI;AAC1H,MAAI,UAAU,KAAK,IAAI,KAAK,IAAI;AAChC,SAAO;AAAA,IACL;AAAA,IACA,MAAM,KAAK,MAAM,WAAW,OAAO,GAAG;AAAA,IACtC,OAAO,KAAK,MAAM,UAAU,OAAO,EAAE;AAAA,IACrC,SAAS,KAAK,MAAM,UAAU,KAAK,EAAE;AAAA,IACrC,SAAS,KAAK,MAAM,UAAU,EAAE;AAAA,IAChC,cAAc,QAAQ,UAAU,IAAI,KAAM,QAAQ,CAAC;AAAA,IACnD,WAAW,SAAS;AAAA,EACtB;AACF;AACA,SAAS,gBAAgB,WAAW,SAAS;AAC3C,MAAI,OAAO,UAAU,MACjB,QAAQ,UAAU,OAClB,UAAU,UAAU,SACpB,UAAU,UAAU;AAExB,MAAI,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,8BAA8B,GAAG,OAAO,GACzF,cAAc,eAAe,aAC7B,cAAc,eAAe,aAC7B,wBAAwB,eAAe,aACvC,cAAc,0BAA0B,SAAS,cAAc;AAEnE,MAAI,oBAAoB,KAAK,IAAI,GAAG,WAAW;AAC/C,MAAI,iBAAiB,cAAc,QAAQ,QAAQ,OAAO,IAAI,WAAW,IAAI,QAAQ,OAAO,iBAAiB;AAC7G,SAAO;AAAA,IACL,MAAM,cAAc,KAAK,QAAQ,MAAM,WAAW;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,QAAQ,SAAS,iBAAiB;AAAA,IAC3C,SAAS,QAAQ,SAAS,iBAAiB;AAAA,EAC7C;AACF;AAEA,IAAI,YAAY,SAAU,kBAAkB;AAC1C,YAAUG,YAAW,gBAAgB;AAErC,MAAI,SAAS,aAAaA,UAAS;AAEnC,WAASA,aAAY;AACnB,QAAI;AAEJ,oBAAgB,MAAMA,UAAS;AAE/B,YAAQ,OAAO,MAAM,MAAM,SAAS;AACpC,UAAM,QAAQ;AAAA,MACZ,OAAO,MAAM,MAAM,SAAS;AAAA,IAC9B;AAEA,UAAM,iBAAiB,WAAY;AACjC,YAAM,WAAW,OAAO,YAAY,WAAY;AAC9C,YAAI,QAAQ,MAAM,MAAM,QAAQ;AAEhC,YAAI,UAAU,GAAG;AACf,gBAAM,cAAc;AAEpB,gBAAM,MAAM,cAAc,MAAM,MAAM,WAAW;AAAA,QACnD,OAAO;AACL,gBAAM,SAAS,SAAU,WAAW;AAClC,mBAAO;AAAA,cACL,OAAO,UAAU,QAAQ;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,GAAG,GAAI;AAAA,IACT;AAEA,UAAM,gBAAgB,WAAY;AAChC,oBAAc,MAAM,QAAQ;AAAA,IAC9B;AAEA,UAAM,UAAU,SAAU,SAAS;AACjC,YAAM,cAAc;AAEpB,YAAM,SAAS,SAAU,WAAW;AAClC,eAAO;AAAA,UACL,OAAO,UAAU,QAAQ;AAAA,QAC3B;AAAA,MACF,GAAG,MAAM,cAAc;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAEA,eAAaA,YAAW,CAAC;AAAA,IACvB,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAO,KAAK,MAAM,eAAW,2BAAa,KAAK,MAAM,UAAU;AAAA,QAC7D,OAAO,KAAK,MAAM;AAAA,MACpB,CAAC,IAAI;AAAA,IACP;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE,sBAAS;AACX,UAAU,YAAY;AAAA,EACpB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AACd;AAEA,IAAI,cAAc,SAAU,kBAAkB;AAC5C,YAAUC,cAAa,gBAAgB;AAEvC,MAAI,SAAS,aAAaA,YAAW;AAErC,WAASA,aAAY,OAAO;AAC1B,QAAI;AAEJ,oBAAgB,MAAMA,YAAW;AAEjC,YAAQ,OAAO,KAAK,MAAM,KAAK;AAC/B,UAAM,UAAU;AAChB,UAAM,mBAAmB,MAAM,yBAAyB;AACxD,UAAM,uBAAuB,MAAM,MAAM,YAAY,IAAI,MAAM;AAC/D,UAAM,aAAa;AACnB,UAAM,aAAa;AACnB,UAAM,qBAAqB;AAE3B,UAAM,OAAO,WAAY;AACvB,UAAIC,aAAY,MAAM,cAAc;AAEpC,UAAI,WAAWA,WAAU,aAAa,CAAC,MAAM,MAAM,WAAW,SAAY,MAAM,MAAM;AAEtF,YAAM,kBAAkBA,YAAW,QAAW,QAAQ;AAAA,IACxD;AAEA,UAAM,wBAAwB,SAAU,KAAK;AAC3C,YAAM,qBAAqB;AAAA,IAC7B;AAEA,UAAM,QAAQ,WAAY;AACxB,UAAI,MAAM,UAAU,EAAG;AACvB,UAAI,2BAA2B,MAAM;AACrC,YAAM,uBAAuB;AAC7B,YAAM,cAAc,2BAA2B,MAAM,yBAAyB,IAAI,2BAA2B;AAE7G,UAAIA,aAAY,MAAM,cAAc;AAEpC,YAAM,kBAAkBA,YAAW,WAAW,MAAM,MAAM,OAAO;AAEjE,UAAI,CAAC,MAAM,MAAM,eAAe,CAACA,WAAU,aAAa,MAAM,MAAM,WAAW;AAC7E,cAAM,WAAW;AAEjB,cAAM,WAAW,OAAO,YAAY,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,MAC3E;AAAA,IACF;AAEA,UAAM,QAAQ,WAAY;AACxB,UAAI,MAAM,SAAS,EAAG;AAEtB,YAAM,WAAW;AAEjB,YAAM,uBAAuB,MAAM,yBAAyB;AAE5D,YAAM,kBAAkB,MAAM,MAAM,WAAW,UAAU,MAAM,MAAM,OAAO;AAAA,IAC9E;AAEA,UAAM,OAAO,WAAY;AACvB,UAAI,MAAM,UAAU,EAAG;AAEvB,YAAM,WAAW;AAEjB,YAAM,uBAAuB,MAAM,yBAAyB;AAC5D,YAAM,aAAa,MAAM,uBAAuB,MAAM;AAEtD,YAAM,kBAAkB,MAAM,cAAc,GAAG,WAAW,MAAM,MAAM,MAAM;AAAA,IAC9E;AAEA,UAAM,YAAY,WAAY;AAC5B,aAAO,MAAM,SAAS,SAAS;AAAA,IACjC;AAEA,UAAM,WAAW,WAAY;AAC3B,aAAO,MAAM,SAAS,QAAQ;AAAA,IAChC;AAEA,UAAM,YAAY,WAAY;AAC5B,aAAO,MAAM,SAAS,SAAS;AAAA,IACjC;AAEA,UAAM,cAAc,WAAY;AAC9B,aAAO,MAAM,SAAS,WAAW;AAAA,IACnC;AAEA,QAAI,MAAM,MAAM;AACd,UAAI,YAAY,MAAM,cAAc;AAEpC,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA,QAAQ,UAAU,YAAY,cAAc;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,YAAM,aAAa;AAAA,IACrB;AAEA,WAAO;AAAA,EACT;AAEA,eAAaD,cAAa,CAAC;AAAA,IACzB,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AAEA,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,QAAS,MAAK,MAAM,QAAQ,KAAK,cAAc,CAAC;AAC/D,UAAI,KAAK,MAAM,UAAW,MAAK,MAAM;AAAA,IACvC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB,WAAW;AAC5C,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AAEA,UAAI,KAAK,MAAM,SAAS,UAAU,MAAM;AACtC,aAAK,mBAAmB,KAAK,yBAAyB;AACtD,aAAK,uBAAuB,KAAK;AACjC,aAAK,aAAa;AAClB,aAAK,kBAAkB,KAAK,cAAc,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AAEA,WAAK,UAAU;AACf,WAAK,WAAW;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,UAAI,cAAc,KAAK,OACnB,OAAO,YAAY,MACnB,MAAM,YAAY,KAClB,YAAY,YAAY,WACxB,aAAa,YAAY,YACzB,WAAW,YAAY;AAC3B,aAAO,cAAc,MAAM;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,KAAK;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,2BAA2B;AACzC,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ,SAAS;AAC/B,WAAK,mBAAmB,QAAQ,OAAO;AAAA,IACzC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,cAAc,KAAK,QAAQ;AAAA,IACpC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ;AAC/B,aAAO,KAAK,MAAM,WAAW;AAAA,IAC/B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,WAAW,QAAQ,UAAU;AAC7D,UAAI,SAAS;AAEb,UAAI,CAAC,KAAK,QAAS;AACnB,UAAI,aAAa,UAAU,aAAa,CAAC,KAAK,MAAM,UAAU;AAC9D,UAAI,mBAAmB,UAAU,aAAa,WAAW;AAEzD,UAAI,cAAc,CAAC,KAAK,MAAM,UAAU;AACtC,aAAK,WAAW;AAAA,MAClB;AAEA,UAAI,SAAS,SAASE,UAAS;AAC7B,YAAI,SAAU,UAAS,OAAO,MAAM,SAAS;AAE7C,YAAI,OAAO,MAAM,eAAe,cAAc,mBAAmB;AAC/D,iBAAO,MAAM,WAAW,WAAW,gBAAgB;AAAA,QACrD;AAAA,MACF;AAEA,aAAO,KAAK,SAAS,SAAU,WAAW;AACxC,YAAI,YAAY,UAAU,UAAU;AAEpC,YAAI,UAAU,aAAa,CAAC,OAAO,MAAM,UAAU;AACjD,sBAAY;AAAA,QACd,WAAW,CAAC,UAAU,cAAc,aAAa;AAC/C,sBAAY;AAAA,QACd;AAEA,eAAO;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF,GAAG,MAAM;AAAA,IACX;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAO,KAAK,MAAM,KAAK,OAAO;AAAA,QAC5B,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,MAAM,KAAK;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB,aAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,eAAe,KAAK,OACpB,cAAc,aAAa,aAC3B,cAAc,aAAa,aAC3B,cAAc,aAAa;AAC/B,UAAI,YAAY,KAAK,MAAM;AAC3B,aAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG;AAAA,QACjD,KAAK,KAAK,OAAO;AAAA,QACjB,OAAO,KAAK;AAAA,QACZ,WAAW,gBAAgB,WAAW;AAAA,UACpC;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,KAAK,YAAY;AACnB,YAAI,eAAe,KAAK,OACpB,QAAQ,aAAa,OACrB,YAAY,aAAa,UACzB,aAAa,aAAa;AAC9B,mBAAO,4BAAc,WAAW;AAAA,UAC9B,KAAK,KAAK;AAAA,UACV;AAAA,UACA;AAAA,QACF,GAAG,SAAS;AAAA,MACd;AAEA,UAAI,eAAe,KAAK,OACpB,YAAY,aAAa,WACzB,WAAW,aAAa,UACxB,WAAW,aAAa,UACxB,WAAW,aAAa;AAC5B,UAAI,cAAc,KAAK,eAAe;AAEtC,UAAI,UAAU;AACZ,eAAO,SAAS,WAAW;AAAA,MAC7B;AAEA,UAAI,YAAY,KAAK,MAAM,UAAU,aAAa,CAAC,UAAU;AAC3D,mBAAO,2BAAa,UAAU;AAAA,UAC5B,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAEA,UAAI,wBAAwB,YAAY,WACpC,OAAO,sBAAsB,MAC7B,QAAQ,sBAAsB,OAC9B,UAAU,sBAAsB,SAChC,UAAU,sBAAsB;AACpC,iBAAO,4BAAc,QAAQ;AAAA,QAC3B;AAAA,MACF,GAAG,YAAY,QAAQ,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,IAC/F;AAAA,EACF,CAAC,CAAC;AAEF,SAAOF;AACT,EAAE,sBAAS;AACX,YAAY,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,8BAA8B,GAAG;AAAA,EAC1F,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AACb,CAAC;AACD,YAAY,YAAY;AAAA,EACtB,UAAM,6BAAU,KAAC,8BAAW,IAAI,GAAG,0BAAQ,wBAAM,CAAC;AAAA,EAClD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,KAAK;AAAA,EACL,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AACd;AAEA,IAAO,mBAAQ;", "names": ["_getPrototypeOf", "o", "_setPrototypeOf", "p", "Countdown", "Countdown$1", "<PERSON><PERSON><PERSON><PERSON>", "onDone"]}